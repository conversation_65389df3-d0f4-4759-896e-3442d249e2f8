# Generated by Django 4.2.7 on 2025-05-22 05:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductivityRecommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recommendation_type', models.CharField(choices=[('break', 'Take a Break'), ('exercise', 'Physical Exercise'), ('meditation', 'Meditation'), ('task_switch', 'Switch Tasks'), ('environment', 'Change Environment'), ('nutrition', 'Nutrition Advice')], max_length=20)),
                ('description', models.TextField()),
                ('expected_impact', models.FloatField()),
                ('duration', models.IntegerField()),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('implemented', models.BooleanField(default=False)),
                ('effectiveness', models.FloatField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='productivity_recommendations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='VoiceMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('speech_rate', models.FloatField()),
                ('pitch_variation', models.FloatField()),
                ('volume', models.FloatField()),
                ('clarity', models.FloatField()),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='voice_metrics', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('age', models.IntegerField(blank=True, null=True)),
                ('occupation', models.CharField(blank=True, max_length=100)),
                ('work_hours_per_day', models.FloatField(default=8.0)),
                ('baseline_productivity', models.FloatField(default=0.0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ProductivitySession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('productivity_score', models.FloatField(blank=True, null=True)),
                ('fatigue_progression', models.JSONField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('recommendations_followed', models.ManyToManyField(blank=True, to='fatique.productivityrecommendation')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='productivity_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='MouseMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_speed', models.FloatField()),
                ('click_frequency', models.FloatField()),
                ('movement_pattern', models.JSONField()),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mouse_metrics', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='KeyboardMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('typing_speed', models.FloatField()),
                ('error_rate', models.FloatField()),
                ('pause_frequency', models.FloatField()),
                ('key_press_duration', models.FloatField()),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='keyboard_metrics', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='FatigueAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fatigue_level', models.CharField(choices=[('low', 'Low'), ('moderate', 'Moderate'), ('high', 'High'), ('severe', 'Severe')], max_length=20)),
                ('fatigue_score', models.FloatField()),
                ('confidence', models.FloatField()),
                ('contributing_factors', models.JSONField()),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fatigue_analyses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Fatigue Analyses',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='FacialMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('eye_blink_rate', models.FloatField()),
                ('eye_closure_duration', models.FloatField()),
                ('facial_expression', models.CharField(max_length=50)),
                ('head_position', models.JSONField()),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='facial_metrics', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='BehavioralData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_type', models.CharField(choices=[('keyboard', 'Keyboard'), ('mouse', 'Mouse'), ('facial', 'Facial'), ('voice', 'Voice')], max_length=20)),
                ('raw_data', models.JSONField()),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='behavioral_data', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
    ]
