# Generated by Django 4.2 on 2025-05-27 08:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('fatique', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TaskPerformance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_label', models.CharField(max_length=255)),
                ('performance_score', models.FloatField()),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='task_performances', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Task Performances',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='AttentionData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attention_level', models.FloatField()),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attention_data', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Attention Data',
                'ordering': ['-timestamp'],
            },
        ),
    ]
