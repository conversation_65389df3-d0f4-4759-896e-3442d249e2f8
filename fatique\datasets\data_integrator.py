"""
Data integrator for combining facial, mouse, and keyboard datasets.
"""

import os
import pandas as pd
import numpy as np
from django.conf import settings
from .dataset_loader import DatasetLoader

class DataIntegrator:
    def __init__(self):
        self.base_dir = settings.BASE_DIR
        self.facial_dir = os.path.join(self.base_dir, 'facial_data')
        self.mouse_dir = os.path.join(self.base_dir, 'mouse_data')
        self.keyboard_dir = os.path.join(self.base_dir, 'keyboard_data')
        self.dataset_loader = DatasetLoader()
        
    def load_keyboard_data(self):
        """Load and preprocess keyboard data."""
        keyboard_file = os.path.join(self.keyboard_dir, 'keystroke_dynamics_dataset.csv')
        if not os.path.exists(keyboard_file):
            raise FileNotFoundError(f"Keyboard dataset not found at {keyboard_file}")
            
        df = pd.read_csv(keyboard_file)
        
        # Calculate required features
        keyboard_features = {
            'typing_speed': df['typing_speed'].mean(),
            'error_rate': df['error_rate'].mean(),
            'pause_frequency': df['pause_frequency'].mean(),
            'key_press_duration': df['key_press_duration'].mean()
        }
        
        return keyboard_features
    
    def load_mouse_data(self):
        """Load and preprocess mouse data."""
        # Assume default values for now
        mouse_features = {
            'movement_speed': 100,  # default value
            'click_frequency': 5  # default value
        }
        return mouse_features
    
    def load_facial_data(self):
        """Load and preprocess facial data."""
        # Assume default values for now
        facial_features = {
            'eye_blink_rate': 20,  # default value
            'eye_closure_duration': 0.3  # default value
        }
        return facial_features
    
    def integrate_datasets(self):
        """
        Integrate all datasets and create a combined dataset.
        
        Returns:
            DataFrame containing the integrated dataset
        """
        try:
            # Load features from each dataset
            keyboard_features = self.load_keyboard_data()
            mouse_features = self.load_mouse_data()
            facial_features = self.load_facial_data()
            
            # Create a new row with all features
            # Note: Voice features and time-based features are set to default values
            # as they're not available in the current datasets
            integrated_data = {
                # Keyboard features
                'typing_speed': keyboard_features['typing_speed'],
                'error_rate': keyboard_features['error_rate'],
                'pause_frequency': keyboard_features['pause_frequency'],
                'key_press_duration': keyboard_features['key_press_duration'],
                
                # Mouse features
                'movement_speed': mouse_features['movement_speed'],
                'click_frequency': mouse_features['click_frequency'],
                
                # Facial features
                'eye_blink_rate': facial_features['eye_blink_rate'],
                'eye_closure_duration': facial_features['eye_closure_duration'],
                
                # Voice features (default values)
                'speech_rate': 120,  # default value
                'pitch_variation': 0.8,  # default value
                'volume': 0.9,  # default value
                'clarity': 0.85,  # default value
                
                # Time-based features (default values)
                'hour_of_day': 12,  # default value
                'day_of_week': 1,  # default value
                
                # Target variable (to be determined)
                'fatigue_score': 0.5  # default value
            }
            
            # Create DataFrame
            df = pd.DataFrame([integrated_data])
            
            # Save integrated dataset
            output_path = os.path.join(self.base_dir, 'fatique', 'datasets', 'data', 'integrated_dataset.csv')
            df.to_csv(output_path, index=False)
            
            return df
            
        except Exception as e:
            print(f"Error integrating datasets: {str(e)}")
            return None
    
    def update_default_dataset(self):
        """
        Update the default dataset with the integrated data.
        """
        try:
            # Load the default dataset
            default_data_path = os.path.join(self.base_dir, 'fatique', 'datasets', 'data', 'default.csv')
            default_df = pd.read_csv(default_data_path)
            
            # Integrate new data
            integrated_df = self.integrate_datasets()
            
            if integrated_df is not None:
                # Append new data to default dataset
                updated_df = pd.concat([default_df, integrated_df], ignore_index=True)
                
                # Save updated dataset
                updated_df.to_csv(default_data_path, index=False)
                
                print("Default dataset updated successfully!")
                return True
            return False
            
        except Exception as e:
            print(f"Error updating default dataset: {str(e)}")
            return False 