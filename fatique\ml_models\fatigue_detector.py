"""
Fatigue detection model using machine learning.
"""

import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import os
import joblib
from django.conf import settings
from django.utils import timezone
from ..models import FatigueAnalysis, KeyboardMetrics, MouseMetrics, FacialMetrics, VoiceMetrics
from .data_preprocessor import DataPreprocessor
from ..datasets.dataset_loader import DatasetLoader
from ..datasets.data_integrator import DataIntegrator


class FatigueDetector:
    def __init__(self, model_path=None):
        """Initialize the fatigue detector."""
        self.model = None
        self.preprocessor = DataPreprocessor()
        self.dataset_loader = DatasetLoader()
        self.data_integrator = DataIntegrator()

        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            self._build_model()

    def _build_model(self):
        """Build the neural network model."""
        model = Sequential([
            Dense(64, activation='relu', input_shape=(14,)),
            BatchNormalization(),
            Dropout(0.3),
            Dense(32, activation='relu'),
            BatchNormalization(),
            Dropout(0.2),
            Dense(16, activation='relu'),
            BatchNormalization(),
            Dropout(0.1),
            Dense(1, activation='sigmoid')  # Output: fatigue score (0-1)
        ])

        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['accuracy', 'mae']
        )

        self.model = model

    def train(self, X=None, y=None, dataset_name='default', epochs=100, batch_size=32, validation_split=0.2):
        """Train the model."""
        if self.model is None:
            self._build_model()

        if X is None or y is None:
            self.data_integrator.update_default_dataset()
            X, y = self.dataset_loader.load_dataset(dataset_name)
            splits = self.dataset_loader.split_dataset(X, y)
            X_train, y_train = splits['train']
            X_val, y_val = splits['val']

            callbacks = [
                EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True),
                ModelCheckpoint(
                    filepath=os.path.join(settings.BASE_DIR, 'fatique', 'ml_models', 'saved_models', 'best_model.h5'),
                    monitor='val_loss',
                    save_best_only=True
                )
            ]

            history = self.model.fit(
                X_train, y_train,
                epochs=epochs,
                batch_size=batch_size,
                validation_data=(X_val, y_val),
                callbacks=callbacks,
                verbose=1
            )
        else:
            history = self.model.fit(
                X, y,
                epochs=epochs,
                batch_size=batch_size,
                validation_split=validation_split,
                verbose=1
            )

        return history

    def predict(self, user, time_window=24):
        """Predict fatigue level for a user."""
        if self.model is None:
            raise ValueError("Model not trained or loaded")

        self.preprocessor.fit(user=user, time_window=time_window)
        X = self.preprocessor.prepare_features(user, time_window)
        fatigue_score = float(self.model.predict(X)[0][0])

        if fatigue_score < 0.2:
            fatigue_level = 'very_low'
        elif fatigue_score < 0.4:
            fatigue_level = 'low'
        elif fatigue_score < 0.6:
            fatigue_level = 'moderate'
        elif fatigue_score < 0.8:
            fatigue_level = 'high'
        else:
            fatigue_level = 'severe'

        confidence = self._calculate_confidence(fatigue_score)
        contributing_factors = self._determine_contributing_factors(user, time_window)
        recommendations = self._generate_recommendations(fatigue_level, contributing_factors)
        self._save_analysis(user, fatigue_score, fatigue_level, confidence, contributing_factors, recommendations)

        return {
            'fatigue_score': fatigue_score * 100,
            'fatigue_level': fatigue_level,
            'confidence': confidence,
            'contributing_factors': contributing_factors,
            'recommendations': recommendations
        }

    def _calculate_confidence(self, fatigue_score):
        """Calculate confidence in the prediction."""
        base_confidence = abs(fatigue_score - 0.5) * 2
        return min(max(base_confidence, 0.5), 0.95)

    def _determine_contributing_factors(self, user, time_window=24):
        """Determine the factors contributing to fatigue."""
        keyboard = KeyboardMetrics.objects.filter(user=user).order_by('-timestamp').first()
        mouse = MouseMetrics.objects.filter(user=user).order_by('-timestamp').first()
        facial = FacialMetrics.objects.filter(user=user).order_by('-timestamp').first()
        voice = VoiceMetrics.objects.filter(user=user).order_by('-timestamp').first()

        factors = {}

        if keyboard:
            if keyboard.error_rate > 10:
                factors['high_error_rate'] = {
                    'value': keyboard.error_rate,
                    'severity': 'high' if keyboard.error_rate > 15 else 'moderate'
                }
            if keyboard.pause_frequency > 5:
                factors['frequent_pauses'] = {
                    'value': keyboard.pause_frequency,
                    'severity': 'high' if keyboard.pause_frequency > 8 else 'moderate'
                }
            if keyboard.key_press_duration > 150:
                factors['slow_key_presses'] = {
                    'value': keyboard.key_press_duration,
                    'severity': 'high' if keyboard.key_press_duration > 200 else 'moderate'
                }

        if mouse:
            if mouse.movement_speed < 100:
                factors['slow_mouse_movement'] = {
                    'value': mouse.movement_speed,
                    'severity': 'high' if mouse.movement_speed < 50 else 'moderate'
                }

        if facial:
            if facial.eye_blink_rate < 10:
                factors['low_blink_rate'] = {
                    'value': facial.eye_blink_rate,
                    'severity': 'high' if facial.eye_blink_rate < 5 else 'moderate'
                }
            if facial.eye_closure_duration > 300:
                factors['long_eye_closures'] = {
                    'value': facial.eye_closure_duration,
                    'severity': 'high' if facial.eye_closure_duration > 500 else 'moderate'
                }

        current_hour = timezone.now().hour
        if 14 <= current_hour <= 16:
            factors['afternoon_slump'] = {'severity': 'moderate'}
        elif current_hour >= 22 or current_hour <= 5:
            factors['late_hours'] = {'severity': 'high'}

        return factors

    def _generate_recommendations(self, fatigue_level, contributing_factors):
        """Generate recommendations based on fatigue level and contributing factors."""
        recommendations = []

        if fatigue_level in ['high', 'severe']:
            recommendations.append("Take a short break (15-20 minutes)")
            recommendations.append("Consider taking a power nap")
            recommendations.append("Stay hydrated and have a light snack")

        for factor, details in contributing_factors.items():
            if factor == 'high_error_rate':
                recommendations.append("Take a short break to refresh your focus")
            elif factor == 'frequent_pauses':
                recommendations.append("Consider taking a longer break to recover")
            elif factor == 'slow_key_presses':
                recommendations.append("Stretch your hands and fingers")
            elif factor == 'slow_mouse_movement':
                recommendations.append("Take a short walk to improve circulation")
            elif factor == 'low_blink_rate':
                recommendations.append("Practice the 20-20-20 rule")
            elif factor == 'long_eye_closures':
                recommendations.append("Take a short break to rest your eyes")
            elif factor == 'afternoon_slump':
                recommendations.append("Consider a short walk or light exercise")
            elif factor == 'late_hours':
                recommendations.append("Consider ending your work session soon")

        return recommendations

    def _save_analysis(self, user, fatigue_score, fatigue_level, confidence, contributing_factors, recommendations):
        """Save fatigue analysis to the database."""
        FatigueAnalysis.objects.create(
            user=user,
            fatigue_score=fatigue_score * 100,
            fatigue_level=fatigue_level,
            confidence=confidence,
            contributing_factors=contributing_factors,
            timestamp=timezone.now()
        )

    def save_model(self, model_path):
        """Save the model to a file."""
        if self.model is None:
            raise ValueError("No model to save")

        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        self.model.save(model_path)

        preprocessor_path = os.path.join(os.path.dirname(model_path), 'fatigue_preprocessor.joblib')
        joblib.dump(self.preprocessor, preprocessor_path)

    def load_model(self, model_path):
        """Load the model from a file."""
        if not os.path.exists(model_path):
            raise ValueError(f"Model file not found: {model_path}")

        self.model = load_model(model_path)

        preprocessor_path = os.path.join(os.path.dirname(model_path), 'fatigue_preprocessor.joblib')
        if os.path.exists(preprocessor_path):
            self.preprocessor = joblib.load(preprocessor_path)
        else:
            self.preprocessor = DataPreprocessor()

    def evaluate(self, X_test=None, y_test=None, dataset_name='default'):
        """Evaluate the model on test data."""
        if self.model is None:
            raise ValueError("Model not trained or loaded")

        if X_test is None or y_test is None:
            X, y = self.dataset_loader.load_dataset(dataset_name)
            splits = self.dataset_loader.split_dataset(X, y)
            X_test, y_test = splits['test']

        y_pred_prob = self.model.predict(X_test)
        y_pred = (y_pred_prob > 0.5).astype(int)

        return {
            'accuracy': accuracy_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred),
            'recall': recall_score(y_test, y_pred),
            'f1_score': f1_score(y_test, y_pred),
            'confusion_matrix': confusion_matrix(y_test, y_pred).tolist()
        }
