<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Collection | Mental Fatigue Detector</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 40px;
        }
        .step-card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            height: 100%;
            margin-bottom: 20px;
        }
        .step-card:hover {
            transform: translateY(-5px);
        }
        .step-icon {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #6a11cb;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background-color: #6a11cb;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 10px;
        }
        .step-content {
            display: none;
        }
        .step-content.active {
            display: block;
        }
        .progress-container {
            margin: 30px 0;
        }
        .btn-primary {
            background-color: #6a11cb;
            border-color: #6a11cb;
        }
        .btn-primary:hover {
            background-color: #5a0cb2;
            border-color: #5a0cb2;
        }
        #webcam {
            width: 100%;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        .metrics-display {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #6a11cb;
        }
        .fatigue-low {
            color: #28a745;
        }
        .fatigue-moderate {
            color: #ffc107;
        }
        .fatigue-high {
            color: #fd7e14;
        }
        .fatigue-severe {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
            <h1 class="display-5 fw-bold">Mental Fatigue Detector</h1>
            <p class="lead">Complete the following steps to collect data for fatigue analysis</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Progress Bar -->
        <div class="progress-container">
            <div class="progress" style="height: 10px;">
                <div id="progressBar" class="progress-bar bg-primary" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
            <div class="d-flex justify-content-between mt-2">
                <span>Start</span>
                <span>Typing</span>
                <span>Mouse</span>
                <span>Facial</span>
                <span>Complete</span>
            </div>
        </div>

        <!-- Step 1: Introduction -->
        <div id="step1" class="step-content active">
            <div class="card step-card">
                <div class="card-body text-center p-5">
                    <div class="step-icon">
                        <i class="bi bi-info-circle"></i>
                    </div>
                    <h2 class="card-title mb-4">Welcome to the Mental Fatigue Detector</h2>
                    <p class="card-text mb-4">
                        To provide you with accurate fatigue analysis and productivity recommendations,
                        we need to collect some data about your current state. This will include:
                    </p>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h5><i class="bi bi-keyboard me-2"></i> Typing Patterns</h5>
                                    <p class="text-muted mb-0">We'll analyze your typing speed, error rate, and pauses</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h5><i class="bi bi-camera-video me-2"></i> Facial Analysis</h5>
                                    <p class="text-muted mb-0">We'll analyze your eye movements and facial expressions</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="card-text mb-4">
                        This process will take approximately 2-3 minutes. Your data is kept private and secure.
                    </p>
                    <button id="startCollectionBtn" class="btn btn-primary btn-lg">Begin Data Collection</button>
                </div>
            </div>
        </div>

        <!-- Step 2: Typing Pattern Collection -->
        <div id="step2" class="step-content">
            <div class="card step-card">
                <div class="card-body p-5">
                    <h2 class="card-title mb-4"><span class="step-number">1</span> Typing Pattern Analysis</h2>
                    <p class="card-text mb-4">
                        Please type the following paragraph in the text area below. This will help us analyze your
                        typing patterns to detect signs of mental fatigue.
                    </p>
                    <div class="card mb-4">
                        <div class="card-body bg-light">
                            <p id="typingPrompt">
                                The quick brown fox jumps over the lazy dog. Pack my box with five dozen liquor jugs.
                                How vexingly quick daft zebras jump! Bright vixens jump; dozy fowl quack.
                                Sphinx of black quartz, judge my vow. Amazingly few discotheques provide jukeboxes.
                            </p>
                        </div>
                    </div>
                    <div class="form-group mb-4">
                        <label for="typingArea" class="form-label">Type the text here:</label>
                        <div class="mb-2">
                            <div class="progress" style="height: 8px;">
                                <div id="typingProgress" class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted">Progress: <span id="typingProgressText">0%</span> | Characters: <span id="characterCount">0</span></small>
                        </div>
                        <textarea id="typingArea" class="form-control" rows="5" placeholder="Start typing here..."></textarea>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="metrics-display text-center">
                                <h6>Typing Speed</h6>
                                <div id="typingSpeedValue" class="metric-value">0 WPM</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metrics-display text-center">
                                <h6>Error Rate</h6>
                                <div id="errorRateValue" class="metric-value">0%</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metrics-display text-center">
                                <h6>Pause Frequency</h6>
                                <div id="pauseFrequencyValue" class="metric-value">0</div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <button id="skipTypingBtn" class="btn btn-outline-secondary">Skip This Step</button>
                        <button id="nextToMouseBtn" class="btn btn-primary" disabled>Continue to Mouse Analysis</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 3: Mouse Movement Analysis -->
        <div id="step3" class="step-content">
            <div class="card step-card">
                <div class="card-body p-5">
                    <h2 class="card-title mb-4"><span class="step-number">2</span> Mouse Movement Analysis</h2>
                    <p class="card-text mb-4">
                        Please move your mouse around the canvas below and click on the red targets as they appear.
                        This will help us analyze your mouse movement patterns and reaction time to detect signs of mental fatigue.
                    </p>
                    <div class="row">
                        <div class="col-md-8">
                            <canvas id="mouseGame" width="600" height="400" style="border:2px solid #6a11cb; background: #f8f9fa; border-radius: 8px; cursor: crosshair;"></canvas>
                            <div class="text-center mt-3">
                                <button id="startMouseGameBtn" class="btn btn-primary">Start Mouse Test</button>
                                <button id="stopMouseGameBtn" class="btn btn-danger ms-2" style="display: none;">Stop Test</button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Mouse Analysis Results</h6>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Score
                                            <span id="mouseScoreValue">0</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Time Remaining
                                            <span id="mouseTimeValue">30s</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Avg Reaction Time
                                            <span id="reactionTimeValue">0ms</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Movement Accuracy
                                            <span id="accuracyValue">0%</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between mt-4">
                        <button id="backToTypingBtn" class="btn btn-outline-secondary">Back to Typing</button>
                        <button id="skipMouseBtn" class="btn btn-outline-secondary">Skip This Step</button>
                        <button id="nextToFacialBtn" class="btn btn-primary" disabled>Continue to Facial Analysis</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 4: Facial Analysis -->
        <div id="step4" class="step-content">
            <div class="card step-card">
                <div class="card-body p-5">
                    <h2 class="card-title mb-4"><span class="step-number">3</span> Facial Analysis</h2>
                    <p class="card-text mb-4">
                        Please allow camera access so we can analyze your facial expressions and eye movements.
                        Look directly at the camera for about 30 seconds.
                    </p>
                    <div class="row">
                        <div class="col-md-6">
                            <div id="webcamContainer">
                                <video id="webcam" autoplay playsinline></video>
                                <div class="d-grid gap-2">
                                    <button id="startCameraBtn" class="btn btn-primary">Start Camera</button>
                                    <button id="stopCameraBtn" class="btn btn-danger" style="display: none;">Stop Camera</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Facial Analysis Results</h5>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Blink Rate
                                            <span id="blinkRateValue">0 blinks/min</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Eye Closure Duration
                                            <span id="eyeClosureValue">0 ms</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Facial Expression
                                            <span id="facialExpressionValue">Neutral</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Analysis Status
                                            <span id="facialAnalysisStatus" class="badge bg-warning">Not Started</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between mt-4">
                        <button id="backToMouseBtn" class="btn btn-outline-secondary">Back to Mouse Analysis</button>
                        <button id="skipFacialBtn" class="btn btn-outline-secondary">Skip This Step</button>
                        <button id="completeCollectionBtn" class="btn btn-primary" disabled>Complete & View Results</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 5: Completion -->
        <div id="step5" class="step-content">
            <div class="card step-card">
                <div class="card-body text-center p-5">
                    <div class="step-icon text-success">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <h2 class="card-title mb-4">Data Collection Complete!</h2>
                    <p class="card-text mb-4">
                        Thank you for providing the data. We've analyzed your typing patterns and facial expressions
                        to determine your current fatigue level.
                    </p>
                    <div class="card mb-4">
                        <div class="card-body">
                            <h5 class="card-title">Your Current Fatigue Level</h5>
                            <div class="d-flex align-items-center justify-content-center">
                                <div id="fatigueLevelIcon" class="display-4 me-3 fatigue-moderate">
                                    <i class="bi bi-emoji-neutral"></i>
                                </div>
                                <div>
                                    <h2 id="fatigueLevelText">Moderate</h2>
                                    <p class="text-muted mb-0">Fatigue Score: <span id="fatigueScoreValue">45</span>/100</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <a href="/dashboard/view/" class="btn btn-primary btn-lg me-3">
                            <i class="bi bi-graph-up me-2"></i>View Dashboard
                        </a>
                        <a href="/data_collection/" class="btn btn-outline-primary btn-lg">
                            <i class="bi bi-arrow-clockwise me-2"></i>Retake Assessment
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Global variables
            let currentStep = 1;
            let typingData = {
                startTime: null,
                keyPresses: [],
                typingSpeed: 0,
                errorRate: 0,
                pauseFrequency: 0
            };
            let facialData = {
                blinkRate: 0,
                eyeClosure: 0,
                expression: 'Neutral',
                analyzed: false
            };
            let mouseData = {
                score: 0,
                reactionTime: 0,
                accuracy: 0,
                movements: [],
                clicks: []
            };
            let typingFatigueScore = 0;
            let facialFatigueScore = 0;
            let mouseFatigueScore = 0;
            let combinedFatigueScore = 45; // Default moderate

            // Get elements
            const progressBar = document.getElementById('progressBar');
            const startCollectionBtn = document.getElementById('startCollectionBtn');
            const skipTypingBtn = document.getElementById('skipTypingBtn');
            const nextToMouseBtn = document.getElementById('nextToMouseBtn');
            const backToTypingBtn = document.getElementById('backToTypingBtn');
            const skipMouseBtn = document.getElementById('skipMouseBtn');
            const nextToFacialBtn = document.getElementById('nextToFacialBtn');
            const backToMouseBtn = document.getElementById('backToMouseBtn');
            const skipFacialBtn = document.getElementById('skipFacialBtn');
            const completeCollectionBtn = document.getElementById('completeCollectionBtn');
            const startCameraBtn = document.getElementById('startCameraBtn');
            const stopCameraBtn = document.getElementById('stopCameraBtn');

            // Mouse game elements
            const startMouseGameBtn = document.getElementById('startMouseGameBtn');
            const stopMouseGameBtn = document.getElementById('stopMouseGameBtn');
            const mouseGame = document.getElementById('mouseGame');
            const mouseScoreValue = document.getElementById('mouseScoreValue');
            const mouseTimeValue = document.getElementById('mouseTimeValue');
            const reactionTimeValue = document.getElementById('reactionTimeValue');
            const accuracyValue = document.getElementById('accuracyValue');

            // Typing elements
            const typingArea = document.getElementById('typingArea');
            const typingPrompt = document.getElementById('typingPrompt');
            const typingSpeedValue = document.getElementById('typingSpeedValue');
            const errorRateValue = document.getElementById('errorRateValue');
            const pauseFrequencyValue = document.getElementById('pauseFrequencyValue');

            // Facial elements
            const webcam = document.getElementById('webcam');
            const blinkRateValue = document.getElementById('blinkRateValue');
            const eyeClosureValue = document.getElementById('eyeClosureValue');
            const facialExpressionValue = document.getElementById('facialExpressionValue');
            const facialAnalysisStatus = document.getElementById('facialAnalysisStatus');

            // Result elements
            const fatigueLevelIcon = document.getElementById('fatigueLevelIcon');
            const fatigueLevelText = document.getElementById('fatigueLevelText');
            const fatigueScoreValue = document.getElementById('fatigueScoreValue');

            // Initialize step navigation
            startCollectionBtn.addEventListener('click', function() {
                goToStep(2);
            });

            skipTypingBtn.addEventListener('click', function() {
                goToStep(3);
            });

            nextToMouseBtn.addEventListener('click', function() {
                goToStep(3);
            });

            backToTypingBtn.addEventListener('click', function() {
                goToStep(2);
            });

            skipMouseBtn.addEventListener('click', function() {
                goToStep(4);
            });

            nextToFacialBtn.addEventListener('click', function() {
                goToStep(4);
            });

            backToMouseBtn.addEventListener('click', function() {
                goToStep(3);
            });

            skipFacialBtn.addEventListener('click', function() {
                goToStep(5);
                updateFinalFatigueLevel();
            });

            completeCollectionBtn.addEventListener('click', function() {
                goToStep(5);
                updateFinalFatigueLevel();
            });

            // Function to navigate between steps
            function goToStep(step) {
                // Hide all steps
                document.querySelectorAll('.step-content').forEach(el => {
                    el.classList.remove('active');
                });

                // Show the current step
                document.getElementById('step' + step).classList.add('active');

                // Update progress bar
                let progress = 0;
                switch(step) {
                    case 1: progress = 0; break;
                    case 2: progress = 20; break;
                    case 3: progress = 40; break;
                    case 4: progress = 60; break;
                    case 5: progress = 100; break;
                }

                progressBar.style.width = progress + '%';
                progressBar.setAttribute('aria-valuenow', progress);

                // Update current step
                currentStep = step;

                // If moving to step 2, start typing analysis
                if (step === 2) {
                    initializeTypingAnalysis();
                }

                // If moving to step 3, initialize mouse game
                if (step === 3) {
                    initializeMouseGame();
                }

                console.log('Moved to step', step);
            }

            // Helper function to check if a key is printable
            function isPrintableKey(key) {
                // Check for single character keys (letters, numbers, symbols)
                if (key.length === 1) {
                    return true;
                }

                // Check for space
                if (key === ' ') {
                    return true;
                }

                // Check for specific printable keys
                const printableKeys = ['Tab', 'Enter'];
                if (printableKeys.includes(key)) {
                    return true;
                }

                return false;
            }

            // Initialize typing analysis
            function initializeTypingAnalysis() {
                // Reset typing data
                typingData = {
                    startTime: null,
                    keyPresses: [],
                    lastKeyTime: 0,
                    errorCount: 0,
                    pauseCount: 0,
                    typingSpeed: 0,
                    errorRate: 0,
                    pauseFrequency: 0,
                    correctCharacters: 0,
                    totalCharacters: 0,
                    backspaceCount: 0,
                    pauseTimes: [],
                    wordCount: 0
                };

                // Clear the text area
                typingArea.value = '';

                // Focus on the text area
                typingArea.focus();

                // Remove existing event listeners
                typingArea.removeEventListener('input', analyzeTyping);
                typingArea.removeEventListener('keydown', trackKeyPress);
                typingArea.removeEventListener('keyup', trackKeyRelease);

                // Add event listeners
                typingArea.addEventListener('input', analyzeTyping);
                typingArea.addEventListener('keydown', trackKeyPress);
                typingArea.addEventListener('keyup', trackKeyRelease);

                console.log('Typing analysis initialized');
            }

            // Track key presses
            function trackKeyPress(e) {
                const currentTime = Date.now();

                // Set start time on first key press
                if (!typingData.startTime) {
                    typingData.startTime = currentTime;
                }

                // Record key press
                const keyPress = {
                    key: e.key,
                    time: currentTime,
                    code: e.code,
                    isBackspace: e.key === 'Backspace',
                    isSpace: e.key === ' ',
                    isPrintable: isPrintableKey(e.key)
                };

                typingData.keyPresses.push(keyPress);

                // Count backspaces as errors
                if (e.key === 'Backspace') {
                    typingData.backspaceCount++;
                    typingData.errorCount++;
                }

                // Check for pause (more than 1.5 seconds between keys)
                if (typingData.lastKeyTime > 0) {
                    const timeDiff = currentTime - typingData.lastKeyTime;
                    if (timeDiff > 1500) {
                        typingData.pauseCount++;
                        typingData.pauseTimes.push(timeDiff);
                        console.log(`Pause detected: ${timeDiff}ms`);
                    }
                }

                // Update last key time
                typingData.lastKeyTime = currentTime;
            }

            // Track key releases
            function trackKeyRelease(e) {
                // Find the corresponding key press
                const keyPresses = typingData.keyPresses.filter(kp => kp.key === e.key);
                if (keyPresses.length > 0) {
                    const lastKeyPress = keyPresses[keyPresses.length - 1];
                    lastKeyPress.duration = Date.now() - lastKeyPress.time;
                }
            }

            // Analyze typing
            function analyzeTyping() {
                if (!typingData.startTime) return;

                // Get the typed text
                const typedText = typingArea.value;
                const promptText = typingPrompt.textContent;

                // Calculate elapsed time in minutes
                const elapsedTime = (Date.now() - typingData.startTime) / 60000;

                if (elapsedTime <= 0) return;

                // Count actual words typed (standard WPM calculation: 5 characters = 1 word)
                const standardWordCount = Math.floor(typedText.length / 5);
                const actualWords = typedText.trim().split(/\s+/).filter(word => word.length > 0);

                // Use the higher of the two for more accurate WPM
                typingData.wordCount = Math.max(standardWordCount, actualWords.length);

                // Calculate typing speed (WPM) - ensure minimum elapsed time
                const minElapsedTime = Math.max(elapsedTime, 0.1); // Minimum 6 seconds
                typingData.typingSpeed = Math.round(typingData.wordCount / minElapsedTime);

                // Calculate character accuracy by comparing with prompt
                let correctCharacters = 0;
                let incorrectCharacters = 0;
                const minLength = Math.min(typedText.length, promptText.length);

                for (let i = 0; i < minLength; i++) {
                    if (typedText[i] === promptText[i]) {
                        correctCharacters++;
                    } else {
                        incorrectCharacters++;
                    }
                }

                // Count extra characters as errors (if user typed more than prompt)
                if (typedText.length > promptText.length) {
                    incorrectCharacters += (typedText.length - promptText.length);
                }

                typingData.correctCharacters = correctCharacters;
                typingData.totalCharacters = typedText.length;

                // Calculate error rate using a more accurate method
                // Method 1: Character accuracy based error rate
                let characterErrorRate = 0;
                if (typedText.length > 0) {
                    characterErrorRate = (incorrectCharacters / typedText.length) * 100;
                }

                // Method 2: Keystroke based error rate (backspaces indicate corrections)
                const printableKeystrokes = typingData.keyPresses.filter(kp => kp.isPrintable).length;
                let keystrokeErrorRate = 0;
                if (printableKeystrokes > 0) {
                    // Backspaces indicate errors that were corrected
                    keystrokeErrorRate = (typingData.backspaceCount / printableKeystrokes) * 100;
                }

                // Use the higher of the two error rates (more conservative approach)
                typingData.errorRate = Math.round(Math.max(characterErrorRate, keystrokeErrorRate));

                // Ensure error rate doesn't exceed 100%
                typingData.errorRate = Math.min(typingData.errorRate, 100);

                // Calculate pause frequency (pauses per minute)
                typingData.pauseFrequency = Math.round(typingData.pauseCount / minElapsedTime);

                // Update displays
                typingSpeedValue.textContent = typingData.typingSpeed + ' WPM';
                errorRateValue.textContent = typingData.errorRate + '%';
                pauseFrequencyValue.textContent = typingData.pauseFrequency;

                // Calculate typing fatigue score
                calculateTypingFatigueScore();

                // Update progress indicator
                const progressPercentage = Math.min((typedText.length / promptText.length) * 100, 100);
                const typingProgress = document.getElementById('typingProgress');
                const typingProgressText = document.getElementById('typingProgressText');
                const characterCount = document.getElementById('characterCount');

                if (typingProgress) {
                    typingProgress.style.width = progressPercentage + '%';
                    typingProgress.setAttribute('aria-valuenow', progressPercentage);
                }
                if (typingProgressText) {
                    typingProgressText.textContent = Math.round(progressPercentage) + '%';
                }
                if (characterCount) {
                    characterCount.textContent = `${typedText.length}/${promptText.length}`;
                }

                // Enable next button if enough text has been typed (at least 30% of prompt)
                if (typedText.length >= promptText.length * 0.3) {
                    nextToMouseBtn.disabled = false;
                }

                // Log progress with detailed metrics
                console.log(`Typing Analysis:`, {
                    progress: `${typedText.length}/${promptText.length} chars (${Math.round(progressPercentage)}%)`,
                    wpm: typingData.typingSpeed,
                    errorRate: typingData.errorRate,
                    correctChars: correctCharacters,
                    incorrectChars: incorrectCharacters,
                    backspaces: typingData.backspaceCount,
                    printableKeystrokes: printableKeystrokes,
                    characterErrorRate: characterErrorRate.toFixed(1) + '%',
                    keystrokeErrorRate: keystrokeErrorRate.toFixed(1) + '%',
                    elapsedMinutes: minElapsedTime.toFixed(2)
                });
            }

            // Calculate typing fatigue score
            function calculateTypingFatigueScore() {
                let score = 0;

                // Low typing speed indicates fatigue
                if (typingData.typingSpeed < 30) {
                    score += 30;
                } else if (typingData.typingSpeed < 50) {
                    score += 15;
                }

                // High error rate indicates fatigue
                if (typingData.errorRate > 10) {
                    score += 30;
                } else if (typingData.errorRate > 5) {
                    score += 15;
                }

                // High pause frequency indicates fatigue
                if (typingData.pauseFrequency > 5) {
                    score += 30;
                } else if (typingData.pauseFrequency > 2) {
                    score += 15;
                }

                // Normalize score to 0-100
                typingFatigueScore = Math.min(Math.max(score, 0), 100);

                console.log('Typing fatigue score:', typingFatigueScore);
            }

            // Initialize mouse game
            function initializeMouseGame() {
                // Reset mouse data
                mouseData = {
                    score: 0,
                    reactionTime: 0,
                    accuracy: 0,
                    movements: [],
                    clicks: [],
                    targets: [],
                    gameStartTime: null,
                    gameRunning: false,
                    totalTargets: 0,
                    hitTargets: 0,
                    missedClicks: 0
                };

                // Clear canvas and set up
                const ctx = mouseGame.getContext('2d');
                ctx.clearRect(0, 0, mouseGame.width, mouseGame.height);

                // Draw initial instructions
                ctx.fillStyle = '#666';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Click "Start Mouse Test" to begin', mouseGame.width/2, mouseGame.height/2);

                // Reset UI
                mouseScoreValue.textContent = '0';
                mouseTimeValue.textContent = '30s';
                reactionTimeValue.textContent = '0ms';
                accuracyValue.textContent = '0%';

                // Add mouse game event listeners (remove existing first)
                startMouseGameBtn.removeEventListener('click', startMouseGame);
                stopMouseGameBtn.removeEventListener('click', stopMouseGame);
                startMouseGameBtn.addEventListener('click', startMouseGame);
                stopMouseGameBtn.addEventListener('click', stopMouseGame);

                console.log('Mouse game initialized');
            }

            // Start mouse game
            function startMouseGame() {
                if (mouseData.gameRunning) return;

                mouseData.gameRunning = true;
                mouseData.gameStartTime = Date.now();
                mouseData.score = 0;
                mouseData.movements = [];
                mouseData.clicks = [];
                mouseData.targets = [];
                mouseData.totalTargets = 0;
                mouseData.hitTargets = 0;
                mouseData.missedClicks = 0;

                // Clear canvas
                const ctx = mouseGame.getContext('2d');
                ctx.clearRect(0, 0, mouseGame.width, mouseGame.height);

                // Update UI
                startMouseGameBtn.style.display = 'none';
                stopMouseGameBtn.style.display = 'inline-block';

                // Start game timer (30 seconds)
                let timeLeft = 30;
                mouseTimeValue.textContent = timeLeft + 's';

                mouseData.gameTimer = setInterval(() => {
                    timeLeft--;
                    mouseTimeValue.textContent = timeLeft + 's';

                    if (timeLeft <= 0) {
                        clearInterval(mouseData.gameTimer);
                        stopMouseGame();
                    }
                }, 1000);

                // Remove existing event listeners
                mouseGame.removeEventListener('mousemove', trackMouseMovement);
                mouseGame.removeEventListener('click', handleMouseClick);

                // Add mouse event listeners
                mouseGame.addEventListener('mousemove', trackMouseMovement);
                mouseGame.addEventListener('click', handleMouseClick);

                // Start spawning targets immediately and then every 1.5-3 seconds
                spawnTarget();
                mouseData.spawnInterval = setInterval(() => {
                    if (mouseData.gameRunning && mouseData.targets.length < 3) {
                        spawnTarget();
                    }
                }, 1500 + Math.random() * 1500);

                console.log('Mouse game started');
            }

            // Stop mouse game
            function stopMouseGame() {
                mouseData.gameRunning = false;

                // Clear all intervals
                if (mouseData.gameTimer) {
                    clearInterval(mouseData.gameTimer);
                }
                if (mouseData.spawnInterval) {
                    clearInterval(mouseData.spawnInterval);
                }

                // Update UI
                startMouseGameBtn.style.display = 'inline-block';
                stopMouseGameBtn.style.display = 'none';

                // Remove event listeners
                mouseGame.removeEventListener('mousemove', trackMouseMovement);
                mouseGame.removeEventListener('click', handleMouseClick);

                // Clear canvas and show completion message
                const ctx = mouseGame.getContext('2d');
                ctx.clearRect(0, 0, mouseGame.width, mouseGame.height);
                ctx.fillStyle = '#28a745';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Game Complete!', mouseGame.width/2, mouseGame.height/2);
                ctx.font = '14px Arial';
                ctx.fillStyle = '#666';
                ctx.fillText(`Final Score: ${mouseData.score}`, mouseGame.width/2, mouseGame.height/2 + 30);

                // Calculate final metrics
                calculateMouseMetrics();

                // Enable next button
                nextToFacialBtn.disabled = false;

                console.log('Mouse game stopped. Final score:', mouseData.score);
            }

            // Track mouse movement
            function trackMouseMovement(e) {
                if (!mouseData.gameRunning) return;

                const rect = mouseGame.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                mouseData.movements.push({
                    x: x,
                    y: y,
                    timestamp: Date.now()
                });
            }

            // Handle mouse clicks
            function handleMouseClick(e) {
                if (!mouseData.gameRunning) return;

                const rect = mouseGame.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                const clickTime = Date.now();

                mouseData.clicks.push({
                    x: x,
                    y: y,
                    timestamp: clickTime,
                    hit: false
                });

                let hitTarget = false;

                // Check if click hit any target
                for (let i = mouseData.targets.length - 1; i >= 0; i--) {
                    const target = mouseData.targets[i];
                    const distance = Math.sqrt((x - target.x) ** 2 + (y - target.y) ** 2);

                    if (distance <= target.radius) {
                        // Hit!
                        hitTarget = true;
                        mouseData.score++;
                        mouseData.hitTargets++;
                        mouseScoreValue.textContent = mouseData.score;

                        // Calculate reaction time
                        const reactionTime = clickTime - target.spawnTime;
                        target.reactionTime = reactionTime;

                        // Mark this click as a hit
                        mouseData.clicks[mouseData.clicks.length - 1].hit = true;
                        mouseData.clicks[mouseData.clicks.length - 1].reactionTime = reactionTime;

                        // Remove target
                        mouseData.targets.splice(i, 1);

                        // Clear and redraw canvas
                        clearCanvas();
                        drawTargets();

                        // Update accuracy in real-time
                        updateRealTimeAccuracy();

                        console.log(`Target hit! Score: ${mouseData.score}, Reaction time: ${reactionTime}ms`);
                        break;
                    }
                }

                if (!hitTarget) {
                    mouseData.missedClicks++;
                    console.log('Missed click at', x, y);
                }

                // Update accuracy in real-time
                updateRealTimeAccuracy();
            }

            // Update real-time accuracy
            function updateRealTimeAccuracy() {
                if (mouseData.clicks.length > 0) {
                    const accuracy = (mouseData.hitTargets / mouseData.clicks.length) * 100;
                    accuracyValue.textContent = Math.round(accuracy) + '%';
                }

                // Update average reaction time
                const hitClicks = mouseData.clicks.filter(click => click.hit && click.reactionTime);
                if (hitClicks.length > 0) {
                    const avgReactionTime = hitClicks.reduce((sum, click) => sum + click.reactionTime, 0) / hitClicks.length;
                    reactionTimeValue.textContent = Math.round(avgReactionTime) + 'ms';
                }
            }

            // Spawn a new target
            function spawnTarget() {
                if (!mouseData.gameRunning) return;

                mouseData.totalTargets++;

                const target = {
                    x: 30 + Math.random() * (mouseGame.width - 60),
                    y: 30 + Math.random() * (mouseGame.height - 60),
                    radius: 25,
                    spawnTime: Date.now(),
                    color: '#ff4444',
                    id: mouseData.totalTargets
                };

                mouseData.targets.push(target);

                // Draw all targets
                drawTargets();

                console.log(`Target ${target.id} spawned at (${Math.round(target.x)}, ${Math.round(target.y)})`);

                // Remove target after 2.5 seconds if not clicked
                target.timeout = setTimeout(() => {
                    const index = mouseData.targets.findIndex(t => t.id === target.id);
                    if (index > -1) {
                        console.log(`Target ${target.id} expired`);
                        mouseData.targets.splice(index, 1);
                        clearCanvas();
                        drawTargets();
                    }
                }, 2500);
            }

            // Clear canvas
            function clearCanvas() {
                const ctx = mouseGame.getContext('2d');
                ctx.clearRect(0, 0, mouseGame.width, mouseGame.height);
            }

            // Draw targets
            function drawTargets() {
                const ctx = mouseGame.getContext('2d');

                // Clear canvas first
                ctx.clearRect(0, 0, mouseGame.width, mouseGame.height);

                // Draw background
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(0, 0, mouseGame.width, mouseGame.height);

                // Draw border
                ctx.strokeStyle = '#6a11cb';
                ctx.lineWidth = 2;
                ctx.strokeRect(0, 0, mouseGame.width, mouseGame.height);

                // Draw targets
                mouseData.targets.forEach(target => {
                    const age = Date.now() - target.spawnTime;
                    const maxAge = 2500; // 2.5 seconds
                    const opacity = Math.max(0.3, 1 - (age / maxAge));

                    // Draw outer ring
                    ctx.beginPath();
                    ctx.arc(target.x, target.y, target.radius + 3, 0, Math.PI * 2);
                    ctx.fillStyle = `rgba(220, 53, 69, ${opacity * 0.3})`;
                    ctx.fill();

                    // Draw main target
                    ctx.beginPath();
                    ctx.arc(target.x, target.y, target.radius, 0, Math.PI * 2);
                    ctx.fillStyle = `rgba(255, 68, 68, ${opacity})`;
                    ctx.fill();

                    // Draw inner circle
                    ctx.beginPath();
                    ctx.arc(target.x, target.y, target.radius * 0.4, 0, Math.PI * 2);
                    ctx.fillStyle = `rgba(255, 255, 255, ${opacity * 0.8})`;
                    ctx.fill();

                    // Draw border
                    ctx.beginPath();
                    ctx.arc(target.x, target.y, target.radius, 0, Math.PI * 2);
                    ctx.strokeStyle = `rgba(204, 0, 0, ${opacity})`;
                    ctx.lineWidth = 2;
                    ctx.stroke();
                });

                // Draw game info
                if (mouseData.gameRunning) {
                    ctx.fillStyle = '#666';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'left';
                    ctx.fillText(`Score: ${mouseData.score}`, 10, 25);
                    ctx.fillText(`Targets: ${mouseData.totalTargets}`, 10, 45);
                }
            }

            // Calculate mouse metrics
            function calculateMouseMetrics() {
                // Calculate average reaction time from successful hits
                const hitClicks = mouseData.clicks.filter(click => click.hit && click.reactionTime);

                if (hitClicks.length > 0) {
                    mouseData.reactionTime = hitClicks.reduce((sum, click) => sum + click.reactionTime, 0) / hitClicks.length;
                } else {
                    // If no hits, use a penalty reaction time
                    mouseData.reactionTime = 1000; // 1 second penalty for no hits
                }

                // Calculate accuracy (successful hits / total clicks)
                if (mouseData.clicks.length > 0) {
                    mouseData.accuracy = (mouseData.hitTargets / mouseData.clicks.length) * 100;
                } else {
                    mouseData.accuracy = 0;
                }

                // Alternative accuracy calculation: hits vs targets spawned
                const targetAccuracy = mouseData.totalTargets > 0 ?
                    (mouseData.hitTargets / mouseData.totalTargets) * 100 : 0;

                // Use the more conservative (lower) accuracy measure
                mouseData.accuracy = Math.min(mouseData.accuracy, targetAccuracy);

                // Update UI
                reactionTimeValue.textContent = Math.round(mouseData.reactionTime) + 'ms';
                accuracyValue.textContent = Math.round(mouseData.accuracy) + '%';

                console.log('Mouse metrics calculated:', {
                    hitTargets: mouseData.hitTargets,
                    totalClicks: mouseData.clicks.length,
                    totalTargets: mouseData.totalTargets,
                    clickAccuracy: mouseData.clicks.length > 0 ? (mouseData.hitTargets / mouseData.clicks.length * 100).toFixed(1) : 0,
                    targetAccuracy: targetAccuracy.toFixed(1),
                    finalAccuracy: mouseData.accuracy.toFixed(1),
                    avgReactionTime: mouseData.reactionTime.toFixed(0)
                });

                // Calculate mouse fatigue score
                calculateMouseFatigueScore();
            }

            // Calculate mouse fatigue score
            function calculateMouseFatigueScore() {
                let score = 0;

                console.log('Calculating mouse fatigue score with data:', {
                    reactionTime: mouseData.reactionTime,
                    accuracy: mouseData.accuracy,
                    gameScore: mouseData.score,
                    totalClicks: mouseData.clicks.length,
                    hitTargets: mouseData.hitTargets,
                    totalTargets: mouseData.totalTargets
                });

                // Slow reaction time indicates fatigue (normal: 300-500ms, tired: >600ms)
                if (mouseData.reactionTime > 800) {
                    score += 35; // Very slow reaction
                } else if (mouseData.reactionTime > 600) {
                    score += 25; // Slow reaction
                } else if (mouseData.reactionTime > 500) {
                    score += 10; // Slightly slow
                }

                // Low accuracy indicates fatigue (good: >80%, poor: <60%)
                if (mouseData.accuracy < 40) {
                    score += 35; // Very poor accuracy
                } else if (mouseData.accuracy < 60) {
                    score += 25; // Poor accuracy
                } else if (mouseData.accuracy < 80) {
                    score += 15; // Below average accuracy
                }

                // Low game score indicates fatigue or poor performance
                const expectedScore = Math.max(8, mouseData.totalTargets * 0.6); // Expect 60% hit rate
                if (mouseData.score < expectedScore * 0.3) {
                    score += 25; // Very low score
                } else if (mouseData.score < expectedScore * 0.5) {
                    score += 15; // Low score
                } else if (mouseData.score < expectedScore * 0.7) {
                    score += 5; // Below average score
                }

                // Factor in missed clicks (clicking without hitting targets)
                const missedClickRatio = mouseData.clicks.length > 0 ?
                    (mouseData.clicks.length - mouseData.hitTargets) / mouseData.clicks.length : 0;

                if (missedClickRatio > 0.7) {
                    score += 15; // Many missed clicks indicate poor coordination
                } else if (missedClickRatio > 0.5) {
                    score += 10;
                }

                // Normalize score to 0-100
                mouseFatigueScore = Math.min(Math.max(score, 0), 100);

                console.log('Mouse fatigue score calculation:', {
                    reactionTimePenalty: mouseData.reactionTime > 600 ? 'Yes' : 'No',
                    accuracyPenalty: mouseData.accuracy < 60 ? 'Yes' : 'No',
                    scorePenalty: mouseData.score < expectedScore * 0.5 ? 'Yes' : 'No',
                    missedClickRatio: missedClickRatio.toFixed(2),
                    finalScore: mouseFatigueScore
                });
            }

            // Initialize facial analysis
            startCameraBtn.addEventListener('click', async function() {
                try {
                    // Request camera access
                    const stream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            width: { ideal: 640 },
                            height: { ideal: 480 },
                            facingMode: 'user'
                        }
                    });

                    // Set video source
                    webcam.srcObject = stream;

                    // Show/hide buttons
                    startCameraBtn.style.display = 'none';
                    stopCameraBtn.style.display = 'block';

                    // Update status
                    facialAnalysisStatus.textContent = 'Analyzing...';
                    facialAnalysisStatus.className = 'badge bg-info';

                    // Start facial analysis
                    startFacialAnalysis();

                    console.log('Camera started');
                } catch (err) {
                    console.error('Error accessing camera:', err);
                    alert('Error accessing camera. Please make sure you have a camera connected and have granted permission to use it.');
                }
            });

            stopCameraBtn.addEventListener('click', function() {
                // Stop the camera
                if (webcam.srcObject) {
                    webcam.srcObject.getTracks().forEach(track => track.stop());
                    webcam.srcObject = null;
                }

                // Show/hide buttons
                startCameraBtn.style.display = 'block';
                stopCameraBtn.style.display = 'none';

                // Update status
                facialAnalysisStatus.textContent = 'Stopped';
                facialAnalysisStatus.className = 'badge bg-secondary';

                console.log('Camera stopped');
            });

            // Start facial analysis
            function startFacialAnalysis() {
                // Reset facial data
                facialData = {
                    blinkRate: 0,
                    eyeClosure: 0,
                    expression: 'Neutral',
                    analyzed: false,
                    blinkCount: 0,
                    startTime: Date.now(),
                    samples: [],
                    avgEyeClosure: 0,
                    blinkPattern: []
                };

                // More realistic facial analysis simulation
                let analysisTime = 0;
                const totalAnalysisTime = 15; // 15 seconds for better accuracy

                const analysisInterval = setInterval(function() {
                    analysisTime += 1;

                    // Simulate more realistic blink detection based on fatigue patterns
                    const fatigueLevel = Math.random(); // 0-1 fatigue simulation

                    // Tired people blink less frequently but with longer closures
                    let blinkProbability = 0.25; // Base 25% chance per second
                    if (fatigueLevel > 0.7) {
                        blinkProbability = 0.15; // Reduced blink rate when tired
                    } else if (fatigueLevel < 0.3) {
                        blinkProbability = 0.35; // Normal/alert blink rate
                    }

                    if (Math.random() < blinkProbability) {
                        facialData.blinkCount++;

                        // Record blink timing
                        facialData.blinkPattern.push({
                            time: Date.now(),
                            fatigueLevel: fatigueLevel
                        });
                    }

                    // Calculate blink rate (blinks per minute)
                    const elapsedMinutes = analysisTime / 60;
                    facialData.blinkRate = Math.round(facialData.blinkCount / elapsedMinutes);

                    // Simulate eye closure duration based on fatigue
                    let eyeClosureDuration;
                    if (fatigueLevel > 0.7) {
                        // Tired: longer eye closures (microsleeps)
                        eyeClosureDuration = Math.floor(Math.random() * 200) + 250; // 250-450ms
                    } else if (fatigueLevel > 0.4) {
                        // Moderate fatigue
                        eyeClosureDuration = Math.floor(Math.random() * 150) + 180; // 180-330ms
                    } else {
                        // Alert: normal blink duration
                        eyeClosureDuration = Math.floor(Math.random() * 100) + 120; // 120-220ms
                    }

                    facialData.eyeClosure = eyeClosureDuration;

                    // Calculate average eye closure over time
                    facialData.samples.push(eyeClosureDuration);
                    if (facialData.samples.length > 10) {
                        facialData.samples.shift(); // Keep only last 10 samples
                    }
                    facialData.avgEyeClosure = Math.round(
                        facialData.samples.reduce((a, b) => a + b, 0) / facialData.samples.length
                    );

                    // More realistic expression detection based on fatigue patterns
                    let expression;
                    if (fatigueLevel > 0.8) {
                        expression = Math.random() < 0.7 ? 'Tired' : 'Distracted';
                    } else if (fatigueLevel > 0.6) {
                        expression = Math.random() < 0.4 ? 'Tired' : (Math.random() < 0.5 ? 'Neutral' : 'Distracted');
                    } else if (fatigueLevel > 0.3) {
                        expression = Math.random() < 0.6 ? 'Neutral' : 'Focused';
                    } else {
                        expression = Math.random() < 0.7 ? 'Focused' : 'Neutral';
                    }

                    facialData.expression = expression;

                    // Update displays
                    blinkRateValue.textContent = facialData.blinkRate + ' blinks/min';
                    eyeClosureValue.textContent = facialData.avgEyeClosure + ' ms';
                    facialExpressionValue.textContent = facialData.expression;

                    // Update progress
                    const progress = (analysisTime / totalAnalysisTime) * 100;
                    facialAnalysisStatus.textContent = `Analyzing... ${Math.round(progress)}%`;

                    // Complete analysis after specified time
                    if (analysisTime >= totalAnalysisTime) {
                        clearInterval(analysisInterval);
                        facialData.analyzed = true;

                        // Calculate facial fatigue score
                        calculateFacialFatigueScore();

                        // Update status
                        facialAnalysisStatus.textContent = 'Complete';
                        facialAnalysisStatus.className = 'badge bg-success';

                        // Enable complete button
                        completeCollectionBtn.disabled = false;

                        console.log('Facial analysis complete:', {
                            blinkRate: facialData.blinkRate,
                            avgEyeClosure: facialData.avgEyeClosure,
                            expression: facialData.expression,
                            totalBlinks: facialData.blinkCount
                        });
                    }
                }, 1000);
            }

            // Calculate facial fatigue score
            function calculateFacialFatigueScore() {
                let score = 0;

                // Analyze blink rate (normal range: 15-20 blinks/min)
                if (facialData.blinkRate < 10) {
                    score += 25; // Very low blink rate indicates fatigue
                } else if (facialData.blinkRate < 15) {
                    score += 15; // Low blink rate
                } else if (facialData.blinkRate > 25) {
                    score += 10; // High blink rate can indicate stress/fatigue
                }

                // Analyze eye closure duration (normal: 100-200ms)
                const avgClosure = facialData.avgEyeClosure || facialData.eyeClosure;
                if (avgClosure > 350) {
                    score += 35; // Very long closures (microsleeps)
                } else if (avgClosure > 250) {
                    score += 25; // Long closures indicate fatigue
                } else if (avgClosure > 200) {
                    score += 10; // Slightly elevated
                }

                // Analyze expression patterns
                if (facialData.expression === 'Tired') {
                    score += 30;
                } else if (facialData.expression === 'Distracted') {
                    score += 20;
                } else if (facialData.expression === 'Focused') {
                    score -= 5; // Focused is good, reduce score
                }

                // Analyze blink pattern consistency
                if (facialData.blinkPattern && facialData.blinkPattern.length > 5) {
                    const avgFatigueFromBlinks = facialData.blinkPattern.reduce((sum, blink) => sum + blink.fatigueLevel, 0) / facialData.blinkPattern.length;
                    score += Math.round(avgFatigueFromBlinks * 20); // Convert 0-1 to 0-20 score
                }

                // Normalize score to 0-100
                facialFatigueScore = Math.min(Math.max(score, 0), 100);

                console.log('Facial fatigue score calculation:', {
                    blinkRate: facialData.blinkRate,
                    avgEyeClosure: avgClosure,
                    expression: facialData.expression,
                    finalScore: facialFatigueScore
                });
            }

            // Update final fatigue level
            function updateFinalFatigueLevel() {
                // Calculate combined score (weighted average)
                let totalWeight = 0;
                let weightedScore = 0;

                if (typingFatigueScore > 0) {
                    weightedScore += typingFatigueScore * 0.33;
                    totalWeight += 0.33;
                }

                if (mouseFatigueScore > 0) {
                    weightedScore += mouseFatigueScore * 0.33;
                    totalWeight += 0.33;
                }

                if (facialFatigueScore > 0) {
                    weightedScore += facialFatigueScore * 0.34;
                    totalWeight += 0.34;
                }

                // If no data is available, default to 45 (moderate)
                combinedFatigueScore = totalWeight > 0 ? weightedScore / totalWeight : 45;

                // Update fatigue level display
                fatigueScoreValue.textContent = Math.round(combinedFatigueScore);

                // Determine level text and class
                let levelText = '';
                let levelClass = '';
                let levelIcon = '';

                if (combinedFatigueScore < 30) {
                    levelText = 'Low';
                    levelClass = 'fatigue-low';
                    levelIcon = '<i class="bi bi-emoji-smile"></i>';
                } else if (combinedFatigueScore < 60) {
                    levelText = 'Moderate';
                    levelClass = 'fatigue-moderate';
                    levelIcon = '<i class="bi bi-emoji-neutral"></i>';
                } else if (combinedFatigueScore < 80) {
                    levelText = 'High';
                    levelClass = 'fatigue-high';
                    levelIcon = '<i class="bi bi-emoji-frown"></i>';
                } else {
                    levelText = 'Severe';
                    levelClass = 'fatigue-severe';
                    levelIcon = '<i class="bi bi-emoji-dizzy"></i>';
                }

                // Update level text and icon
                fatigueLevelText.textContent = levelText;
                fatigueLevelIcon.innerHTML = levelIcon;
                fatigueLevelIcon.className = 'display-4 me-3 ' + levelClass;

                // Calculate confidence based on data quality
                let confidence = 0.5; // Base confidence
                let dataQualityCount = 0;

                if (typingData && typingData.typingSpeed > 0) {
                    confidence += 0.15;
                    dataQualityCount++;
                }
                if (mouseData && mouseData.score > 0) {
                    confidence += 0.15;
                    dataQualityCount++;
                }
                if (facialData && facialData.analyzed) {
                    confidence += 0.20;
                    dataQualityCount++;
                }

                // Boost confidence if all data sources are available
                if (dataQualityCount === 3) {
                    confidence += 0.10;
                }

                confidence = Math.min(confidence, 0.95); // Cap at 95%

                // Store fatigue score in session storage to pass to dashboard
                sessionStorage.setItem('fatigueScore', combinedFatigueScore);
                sessionStorage.setItem('fatigueLevel', levelText);
                sessionStorage.setItem('mlConfidence', confidence);
                sessionStorage.setItem('typingData', JSON.stringify(typingData));
                sessionStorage.setItem('mouseData', JSON.stringify(mouseData));
                sessionStorage.setItem('facialData', JSON.stringify(facialData));
                sessionStorage.setItem('typingFatigueScore', typingFatigueScore);
                sessionStorage.setItem('mouseFatigueScore', mouseFatigueScore);
                sessionStorage.setItem('facialFatigueScore', facialFatigueScore);

                // Send data to ML model for enhanced analysis
                sendDataToMLModel();

                console.log('Final fatigue score:', combinedFatigueScore);
            }

            // Send collected data to ML model for analysis
            function sendDataToMLModel() {
                const dataToSend = {
                    typing_data: typingData,
                    mouse_data: mouseData,
                    facial_data: facialData
                };

                fetch('/api/fatigue/analyze/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(dataToSend)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Store ML analysis results
                        sessionStorage.setItem('mlAnalysis', JSON.stringify(data.fatigue_analysis));
                        sessionStorage.setItem('mlRecommendations', JSON.stringify(data.recommendations));

                        // Store ML confidence if available
                        if (data.fatigue_analysis && data.fatigue_analysis.confidence !== undefined) {
                            sessionStorage.setItem('mlConfidence', data.fatigue_analysis.confidence);
                        }

                        console.log('ML Analysis completed:', data);

                        // Update the final fatigue level with ML results
                        updateFinalFatigueLevelWithML(data.fatigue_analysis);
                    } else {
                        console.error('ML Analysis failed:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error sending data to ML model:', error);
                });
            }

            // Update final fatigue level with ML results
            function updateFinalFatigueLevelWithML(mlAnalysis) {
                if (mlAnalysis && mlAnalysis.combined_fatigue_score !== undefined) {
                    const mlFatigueScore = mlAnalysis.combined_fatigue_score;

                    // Update the display with ML-enhanced results
                    fatigueScoreValue.textContent = Math.round(mlFatigueScore);

                    // Determine level text and class based on ML score
                    let levelText = '';
                    let levelClass = '';
                    let levelIcon = '';

                    if (mlFatigueScore < 30) {
                        levelText = 'Low';
                        levelClass = 'fatigue-low';
                        levelIcon = '<i class="bi bi-emoji-smile"></i>';
                    } else if (mlFatigueScore < 60) {
                        levelText = 'Moderate';
                        levelClass = 'fatigue-moderate';
                        levelIcon = '<i class="bi bi-emoji-neutral"></i>';
                    } else if (mlFatigueScore < 80) {
                        levelText = 'High';
                        levelClass = 'fatigue-high';
                        levelIcon = '<i class="bi bi-emoji-frown"></i>';
                    } else {
                        levelText = 'Severe';
                        levelClass = 'fatigue-severe';
                        levelIcon = '<i class="bi bi-emoji-dizzy"></i>';
                    }

                    // Update level text and icon
                    fatigueLevelText.textContent = levelText;
                    fatigueLevelIcon.innerHTML = levelIcon;
                    fatigueLevelIcon.className = 'display-4 me-3 ' + levelClass;

                    // Update session storage with ML-enhanced results
                    sessionStorage.setItem('fatigueScore', mlFatigueScore);
                    sessionStorage.setItem('fatigueLevel', levelText);

                    console.log('Updated with ML-enhanced fatigue score:', mlFatigueScore);
                }
            }

            // Helper function to get CSRF token
            function getCookie(name) {
                if (name === 'csrftoken') {
                    // Try to get from meta tag first
                    const metaTag = document.querySelector('meta[name="csrf-token"]');
                    if (metaTag) {
                        return metaTag.getAttribute('content');
                    }
                }

                // Fallback to cookie method
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }

        });
    </script>
</body>
</html>
