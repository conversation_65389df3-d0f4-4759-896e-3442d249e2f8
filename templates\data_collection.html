<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Collection | Mental Fatigue Detector</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 40px;
        }
        .step-card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            height: 100%;
            margin-bottom: 20px;
        }
        .step-card:hover {
            transform: translateY(-5px);
        }
        .step-icon {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #6a11cb;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background-color: #6a11cb;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 10px;
        }
        .step-content {
            display: none;
        }
        .step-content.active {
            display: block;
        }
        .progress-container {
            margin: 30px 0;
        }
        .btn-primary {
            background-color: #6a11cb;
            border-color: #6a11cb;
        }
        .btn-primary:hover {
            background-color: #5a0cb2;
            border-color: #5a0cb2;
        }
        #webcam {
            width: 100%;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        .metrics-display {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #6a11cb;
        }
        .fatigue-low {
            color: #28a745;
        }
        .fatigue-moderate {
            color: #ffc107;
        }
        .fatigue-high {
            color: #fd7e14;
        }
        .fatigue-severe {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
            <h1 class="display-5 fw-bold">Mental Fatigue Detector</h1>
            <p class="lead">Complete the following steps to collect data for fatigue analysis</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Progress Bar -->
        <div class="progress-container">
            <div class="progress" style="height: 10px;">
                <div id="progressBar" class="progress-bar bg-primary" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
            <div class="d-flex justify-content-between mt-2">
                <span>Start</span>
                <span>Typing</span>
                <span>Facial</span>
                <span>Complete</span>
            </div>
        </div>

        <!-- Step 1: Introduction -->
        <div id="step1" class="step-content active">
            <div class="card step-card">
                <div class="card-body text-center p-5">
                    <div class="step-icon">
                        <i class="bi bi-info-circle"></i>
                    </div>
                    <h2 class="card-title mb-4">Welcome to the Mental Fatigue Detector</h2>
                    <p class="card-text mb-4">
                        To provide you with accurate fatigue analysis and productivity recommendations,
                        we need to collect some data about your current state. This will include:
                    </p>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h5><i class="bi bi-keyboard me-2"></i> Typing Patterns</h5>
                                    <p class="text-muted mb-0">We'll analyze your typing speed, error rate, and pauses</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h5><i class="bi bi-camera-video me-2"></i> Facial Analysis</h5>
                                    <p class="text-muted mb-0">We'll analyze your eye movements and facial expressions</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="card-text mb-4">
                        This process will take approximately 2-3 minutes. Your data is kept private and secure.
                    </p>
                    <button id="startCollectionBtn" class="btn btn-primary btn-lg">Begin Data Collection</button>
                </div>
            </div>
        </div>

        <!-- Step 2: Typing Pattern Collection -->
        <div id="step2" class="step-content">
            <div class="card step-card">
                <div class="card-body p-5">
                    <h2 class="card-title mb-4"><span class="step-number">1</span> Typing Pattern Analysis</h2>
                    <p class="card-text mb-4">
                        Please type the following paragraph in the text area below. This will help us analyze your
                        typing patterns to detect signs of mental fatigue.
                    </p>
                    <div class="card mb-4">
                        <div class="card-body bg-light">
                            <p id="typingPrompt">
                                The quick brown fox jumps over the lazy dog. Pack my box with five dozen liquor jugs.
                                How vexingly quick daft zebras jump! Bright vixens jump; dozy fowl quack.
                                Sphinx of black quartz, judge my vow. Amazingly few discotheques provide jukeboxes.
                            </p>
                        </div>
                    </div>
                    <div class="form-group mb-4">
                        <label for="typingArea" class="form-label">Type the text here:</label>
                        <textarea id="typingArea" class="form-control" rows="5" placeholder="Start typing here..."></textarea>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="metrics-display text-center">
                                <h6>Typing Speed</h6>
                                <div id="typingSpeedValue" class="metric-value">0 WPM</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metrics-display text-center">
                                <h6>Error Rate</h6>
                                <div id="errorRateValue" class="metric-value">0%</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metrics-display text-center">
                                <h6>Pause Frequency</h6>
                                <div id="pauseFrequencyValue" class="metric-value">0</div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <button id="skipTypingBtn" class="btn btn-outline-secondary">Skip This Step</button>
                        <button id="nextToFacialBtn" class="btn btn-primary" disabled>Continue to Facial Analysis</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 3: Facial Analysis -->
        <div id="step3" class="step-content">
            <div class="card step-card">
                <div class="card-body p-5">
                    <h2 class="card-title mb-4"><span class="step-number">2</span> Facial Analysis</h2>
                    <p class="card-text mb-4">
                        Please allow camera access so we can analyze your facial expressions and eye movements.
                        Look directly at the camera for about 30 seconds.
                    </p>
                    <div class="row">
                        <div class="col-md-6">
                            <div id="webcamContainer">
                                <video id="webcam" autoplay playsinline></video>
                                <div class="d-grid gap-2">
                                    <button id="startCameraBtn" class="btn btn-primary">Start Camera</button>
                                    <button id="stopCameraBtn" class="btn btn-danger" style="display: none;">Stop Camera</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Facial Analysis Results</h5>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Blink Rate
                                            <span id="blinkRateValue">0 blinks/min</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Eye Closure Duration
                                            <span id="eyeClosureValue">0 ms</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Facial Expression
                                            <span id="facialExpressionValue">Neutral</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Analysis Status
                                            <span id="facialAnalysisStatus" class="badge bg-warning">Not Started</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between mt-4">
                        <button id="backToTypingBtn" class="btn btn-outline-secondary">Back to Typing</button>
                        <button id="skipFacialBtn" class="btn btn-outline-secondary">Skip This Step</button>
                        <button id="completeCollectionBtn" class="btn btn-primary" disabled>Complete & View Results</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 4: Completion -->
        <div id="step4" class="step-content">
            <div class="card step-card">
                <div class="card-body text-center p-5">
                    <div class="step-icon text-success">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <h2 class="card-title mb-4">Data Collection Complete!</h2>
                    <p class="card-text mb-4">
                        Thank you for providing the data. We've analyzed your typing patterns and facial expressions
                        to determine your current fatigue level.
                    </p>
                    <div class="card mb-4">
                        <div class="card-body">
                            <h5 class="card-title">Your Current Fatigue Level</h5>
                            <div class="d-flex align-items-center justify-content-center">
                                <div id="fatigueLevelIcon" class="display-4 me-3 fatigue-moderate">
                                    <i class="bi bi-emoji-neutral"></i>
                                </div>
                                <div>
                                    <h2 id="fatigueLevelText">Moderate</h2>
                                    <p class="text-muted mb-0">Fatigue Score: <span id="fatigueScoreValue">45</span>/100</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <a href="/dashboard/view/" class="btn btn-primary btn-lg">View Dashboard</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Global variables
            let currentStep = 1;
            let typingData = {
                startTime: null,
                keyPresses: [],
                typingSpeed: 0,
                errorRate: 0,
                pauseFrequency: 0
            };
            let facialData = {
                blinkRate: 0,
                eyeClosure: 0,
                expression: 'Neutral',
                analyzed: false
            };
            let typingFatigueScore = 0;
            let facialFatigueScore = 0;
            let combinedFatigueScore = 45; // Default moderate

            // Get elements
            const progressBar = document.getElementById('progressBar');
            const startCollectionBtn = document.getElementById('startCollectionBtn');
            const skipTypingBtn = document.getElementById('skipTypingBtn');
            const nextToFacialBtn = document.getElementById('nextToFacialBtn');
            const backToTypingBtn = document.getElementById('backToTypingBtn');
            const skipFacialBtn = document.getElementById('skipFacialBtn');
            const completeCollectionBtn = document.getElementById('completeCollectionBtn');
            const startCameraBtn = document.getElementById('startCameraBtn');
            const stopCameraBtn = document.getElementById('stopCameraBtn');

            // Typing elements
            const typingArea = document.getElementById('typingArea');
            const typingPrompt = document.getElementById('typingPrompt');
            const typingSpeedValue = document.getElementById('typingSpeedValue');
            const errorRateValue = document.getElementById('errorRateValue');
            const pauseFrequencyValue = document.getElementById('pauseFrequencyValue');

            // Facial elements
            const webcam = document.getElementById('webcam');
            const blinkRateValue = document.getElementById('blinkRateValue');
            const eyeClosureValue = document.getElementById('eyeClosureValue');
            const facialExpressionValue = document.getElementById('facialExpressionValue');
            const facialAnalysisStatus = document.getElementById('facialAnalysisStatus');

            // Result elements
            const fatigueLevelIcon = document.getElementById('fatigueLevelIcon');
            const fatigueLevelText = document.getElementById('fatigueLevelText');
            const fatigueScoreValue = document.getElementById('fatigueScoreValue');

            // Initialize step navigation
            startCollectionBtn.addEventListener('click', function() {
                goToStep(2);
            });

            skipTypingBtn.addEventListener('click', function() {
                goToStep(3);
            });

            nextToFacialBtn.addEventListener('click', function() {
                goToStep(3);
            });

            backToTypingBtn.addEventListener('click', function() {
                goToStep(2);
            });

            skipFacialBtn.addEventListener('click', function() {
                goToStep(4);
                updateFinalFatigueLevel();
            });

            completeCollectionBtn.addEventListener('click', function() {
                goToStep(4);
                updateFinalFatigueLevel();
            });

            // Function to navigate between steps
            function goToStep(step) {
                // Hide all steps
                document.querySelectorAll('.step-content').forEach(el => {
                    el.classList.remove('active');
                });

                // Show the current step
                document.getElementById('step' + step).classList.add('active');

                // Update progress bar
                let progress = 0;
                switch(step) {
                    case 1: progress = 0; break;
                    case 2: progress = 33; break;
                    case 3: progress = 66; break;
                    case 4: progress = 100; break;
                }

                progressBar.style.width = progress + '%';
                progressBar.setAttribute('aria-valuenow', progress);

                // Update current step
                currentStep = step;

                // If moving to step 2, start typing analysis
                if (step === 2) {
                    initializeTypingAnalysis();
                }

                console.log('Moved to step', step);
            }

            // Initialize typing analysis
            function initializeTypingAnalysis() {
                // Reset typing data
                typingData = {
                    startTime: Date.now(),
                    keyPresses: [],
                    lastKeyTime: 0,
                    errorCount: 0,
                    pauseCount: 0,
                    typingSpeed: 0,
                    errorRate: 0,
                    pauseFrequency: 0
                };

                // Clear the text area
                typingArea.value = '';

                // Focus on the text area
                typingArea.focus();

                // Add event listeners
                typingArea.addEventListener('input', analyzeTyping);
                typingArea.addEventListener('keydown', trackKeyPress);

                console.log('Typing analysis initialized');
            }

            // Track key presses
            function trackKeyPress(e) {
                const currentTime = Date.now();

                // Record key press
                typingData.keyPresses.push({
                    key: e.key,
                    time: currentTime
                });

                // Check for backspace (error correction)
                if (e.key === 'Backspace') {
                    typingData.errorCount++;
                }

                // Check for pause
                if (typingData.lastKeyTime > 0 && (currentTime - typingData.lastKeyTime) > 1000) {
                    typingData.pauseCount++;
                }

                // Update last key time
                typingData.lastKeyTime = currentTime;
            }

            // Analyze typing
            function analyzeTyping() {
                // Get the typed text
                const typedText = typingArea.value;
                const promptText = typingPrompt.textContent;

                // Calculate elapsed time in minutes
                const elapsedTime = (Date.now() - typingData.startTime) / 60000;

                // Calculate typing speed (WPM)
                // Assuming 5 characters per word on average
                const characterCount = typedText.length;
                const wordCount = characterCount / 5;
                typingData.typingSpeed = Math.round(wordCount / Math.max(elapsedTime, 0.01));

                // Calculate error rate
                typingData.errorRate = Math.round((typingData.errorCount / Math.max(typingData.keyPresses.length, 1)) * 100);

                // Calculate pause frequency (pauses per minute)
                typingData.pauseFrequency = Math.round(typingData.pauseCount / Math.max(elapsedTime, 0.01));

                // Update displays
                typingSpeedValue.textContent = typingData.typingSpeed + ' WPM';
                errorRateValue.textContent = typingData.errorRate + '%';
                pauseFrequencyValue.textContent = typingData.pauseFrequency;

                // Calculate typing fatigue score
                calculateTypingFatigueScore();

                // Enable next button if enough text has been typed
                if (typedText.length > promptText.length * 0.5) {
                    nextToFacialBtn.disabled = false;
                }
            }

            // Calculate typing fatigue score
            function calculateTypingFatigueScore() {
                let score = 0;

                // Low typing speed indicates fatigue
                if (typingData.typingSpeed < 30) {
                    score += 30;
                } else if (typingData.typingSpeed < 50) {
                    score += 15;
                }

                // High error rate indicates fatigue
                if (typingData.errorRate > 10) {
                    score += 30;
                } else if (typingData.errorRate > 5) {
                    score += 15;
                }

                // High pause frequency indicates fatigue
                if (typingData.pauseFrequency > 5) {
                    score += 30;
                } else if (typingData.pauseFrequency > 2) {
                    score += 15;
                }

                // Normalize score to 0-100
                typingFatigueScore = Math.min(Math.max(score, 0), 100);

                console.log('Typing fatigue score:', typingFatigueScore);
            }

            // Initialize facial analysis
            startCameraBtn.addEventListener('click', async function() {
                try {
                    // Request camera access
                    const stream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            width: { ideal: 640 },
                            height: { ideal: 480 },
                            facingMode: 'user'
                        }
                    });

                    // Set video source
                    webcam.srcObject = stream;

                    // Show/hide buttons
                    startCameraBtn.style.display = 'none';
                    stopCameraBtn.style.display = 'block';

                    // Update status
                    facialAnalysisStatus.textContent = 'Analyzing...';
                    facialAnalysisStatus.className = 'badge bg-info';

                    // Start facial analysis
                    startFacialAnalysis();

                    console.log('Camera started');
                } catch (err) {
                    console.error('Error accessing camera:', err);
                    alert('Error accessing camera. Please make sure you have a camera connected and have granted permission to use it.');
                }
            });

            stopCameraBtn.addEventListener('click', function() {
                // Stop the camera
                if (webcam.srcObject) {
                    webcam.srcObject.getTracks().forEach(track => track.stop());
                    webcam.srcObject = null;
                }

                // Show/hide buttons
                startCameraBtn.style.display = 'block';
                stopCameraBtn.style.display = 'none';

                // Update status
                facialAnalysisStatus.textContent = 'Stopped';
                facialAnalysisStatus.className = 'badge bg-secondary';

                console.log('Camera stopped');
            });

            // Start facial analysis
            function startFacialAnalysis() {
                // Reset facial data
                facialData = {
                    blinkRate: 0,
                    eyeClosure: 0,
                    expression: 'Neutral',
                    analyzed: false,
                    blinkCount: 0,
                    startTime: Date.now()
                };

                // Simulate facial analysis (in a real app, this would use computer vision)
                let analysisTime = 0;
                const analysisInterval = setInterval(function() {
                    analysisTime += 1;

                    // Simulate blink detection
                    if (Math.random() < 0.3) { // 30% chance of detecting a blink
                        facialData.blinkCount++;
                    }

                    // Calculate blink rate
                    const elapsedMinutes = (Date.now() - facialData.startTime) / 60000;
                    facialData.blinkRate = Math.round(facialData.blinkCount / Math.max(elapsedMinutes, 0.01));

                    // Simulate eye closure duration (100-400ms)
                    facialData.eyeClosure = Math.floor(Math.random() * 300) + 100;

                    // Simulate facial expression detection
                    const expressions = ['Neutral', 'Tired', 'Focused', 'Distracted'];
                    const expressionIndex = Math.floor(Math.random() * expressions.length);
                    facialData.expression = expressions[expressionIndex];

                    // Update displays
                    blinkRateValue.textContent = facialData.blinkRate + ' blinks/min';
                    eyeClosureValue.textContent = facialData.eyeClosure + ' ms';
                    facialExpressionValue.textContent = facialData.expression;

                    // After 10 seconds, complete the analysis
                    if (analysisTime >= 10) {
                        clearInterval(analysisInterval);
                        facialData.analyzed = true;

                        // Calculate facial fatigue score
                        calculateFacialFatigueScore();

                        // Update status
                        facialAnalysisStatus.textContent = 'Complete';
                        facialAnalysisStatus.className = 'badge bg-success';

                        // Enable complete button
                        completeCollectionBtn.disabled = false;

                        console.log('Facial analysis complete');
                    }
                }, 1000);
            }

            // Calculate facial fatigue score
            function calculateFacialFatigueScore() {
                let score = 0;

                // Low blink rate indicates fatigue
                if (facialData.blinkRate < 10) {
                    score += 20;
                } else if (facialData.blinkRate > 30) {
                    score += 15; // Excessive blinking can also indicate fatigue
                }

                // Long eye closure indicates fatigue
                if (facialData.eyeClosure > 300) {
                    score += 30;
                } else if (facialData.eyeClosure > 200) {
                    score += 15;
                }

                // Expression indicates fatigue
                if (facialData.expression === 'Tired') {
                    score += 25;
                } else if (facialData.expression === 'Distracted') {
                    score += 15;
                }

                // Normalize score to 0-100
                facialFatigueScore = Math.min(Math.max(score, 0), 100);

                console.log('Facial fatigue score:', facialFatigueScore);
            }

            // Update final fatigue level
            function updateFinalFatigueLevel() {
                // Calculate combined score (weighted average)
                let totalWeight = 0;
                let weightedScore = 0;

                if (typingFatigueScore > 0) {
                    weightedScore += typingFatigueScore * 0.5;
                    totalWeight += 0.5;
                }

                if (facialFatigueScore > 0) {
                    weightedScore += facialFatigueScore * 0.5;
                    totalWeight += 0.5;
                }

                // If no data is available, default to 45 (moderate)
                combinedFatigueScore = totalWeight > 0 ? weightedScore / totalWeight : 45;

                // Update fatigue level display
                fatigueScoreValue.textContent = Math.round(combinedFatigueScore);

                // Determine level text and class
                let levelText = '';
                let levelClass = '';
                let levelIcon = '';

                if (combinedFatigueScore < 30) {
                    levelText = 'Low';
                    levelClass = 'fatigue-low';
                    levelIcon = '<i class="bi bi-emoji-smile"></i>';
                } else if (combinedFatigueScore < 60) {
                    levelText = 'Moderate';
                    levelClass = 'fatigue-moderate';
                    levelIcon = '<i class="bi bi-emoji-neutral"></i>';
                } else if (combinedFatigueScore < 80) {
                    levelText = 'High';
                    levelClass = 'fatigue-high';
                    levelIcon = '<i class="bi bi-emoji-frown"></i>';
                } else {
                    levelText = 'Severe';
                    levelClass = 'fatigue-severe';
                    levelIcon = '<i class="bi bi-emoji-dizzy"></i>';
                }

                // Update level text and icon
                fatigueLevelText.textContent = levelText;
                fatigueLevelIcon.innerHTML = levelIcon;
                fatigueLevelIcon.className = 'display-4 me-3 ' + levelClass;

                // Store fatigue score in session storage to pass to dashboard
                sessionStorage.setItem('fatigueScore', combinedFatigueScore);
                sessionStorage.setItem('fatigueLevel', levelText);
                sessionStorage.setItem('typingData', JSON.stringify(typingData));
                sessionStorage.setItem('facialData', JSON.stringify(facialData));

                console.log('Final fatigue score:', combinedFatigueScore);
            }

        });
    </script>
</body>
</html>
