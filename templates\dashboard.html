<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard | Mental Fatigue Detector</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 40px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .fatigue-icon {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #6a11cb;
        }
        .session-card {
            border-left: 4px solid #6a11cb;
        }
        .recommendation-card {
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
            <h1 class="display-5 fw-bold">Mental Fatigue Dashboard</h1>
            <p class="lead">Monitor your mental fatigue levels and get personalized recommendations</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Current Status -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="fatigue-icon">
                            <i id="fatigueIcon" class="bi bi-emoji-neutral"></i>
                        </div>
                        <h2 class="card-title">Current Fatigue Level</h2>
                        <div class="d-flex justify-content-center align-items-center mb-3">
                            <span id="fatigueLevel" class="badge bg-warning me-2">Moderate</span>
                            <span class="metric-value" id="currentFatigueScore">45</span>
                            <span class="ms-2">/100</span>
                        </div>
                        <button id="startSessionBtn" class="btn btn-primary btn-lg">Start Session</button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title">Session Status</h3>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span>Status:</span>
                            <span id="sessionStatus" class="badge bg-secondary">Not Started</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Duration:</span>
                            <span id="sessionTimer" class="metric-value">00:00:00</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Session History -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title">Session History</h3>
                        <div id="sessionsList">
                            <!-- Sessions will be added here dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommendations -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title">Recommendations</h3>
                        <div id="recommendationsList">
                            <!-- Recommendations will be added here dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let currentSession = null;
            let sessionTimer = null;
            let sessionStartTime = null;
            let sessionData = {
                typing: [],
                mouse: [],
                facial: [],
                fatigueScores: []
            };

            // Start Session Button
            document.getElementById('startSessionBtn').addEventListener('click', function() {
                if (!currentSession) {
                    startNewSession();
                } else {
                    endCurrentSession();
                }
            });

            function startNewSession() {
                // Create new session
                currentSession = {
                    id: Date.now(),
                    startTime: new Date(),
                    status: 'active'
                };

                // Update UI
                document.getElementById('startSessionBtn').textContent = 'End Session';
                document.getElementById('startSessionBtn').className = 'btn btn-danger';
                document.getElementById('sessionStatus').textContent = 'Active';
                document.getElementById('sessionStatus').className = 'badge bg-success';

                // Start session timer
                sessionStartTime = Date.now();
                updateSessionTimer();
                sessionTimer = setInterval(updateSessionTimer, 1000);

                // Start data collection
                startDataCollection();

                // Add to sessions list
                addSessionToList(currentSession);
            }

            function endCurrentSession() {
                if (!currentSession) return;

                // Update session data
                currentSession.endTime = new Date();
                currentSession.duration = Math.floor((currentSession.endTime - currentSession.startTime) / 1000);
                currentSession.status = 'completed';

                // Stop timer
                clearInterval(sessionTimer);
                sessionTimer = null;

                // Update UI
                document.getElementById('startSessionBtn').textContent = 'Start New Session';
                document.getElementById('startSessionBtn').className = 'btn btn-primary';
                document.getElementById('sessionStatus').textContent = 'Completed';
                document.getElementById('sessionStatus').className = 'badge bg-secondary';
                document.getElementById('sessionTimer').textContent = '00:00:00';

                // Stop data collection
                stopDataCollection();

                // Save session data
                saveSessionData(currentSession);

                // Update session in list
                updateSessionInList(currentSession);

                // Generate recommendations
                generateRecommendations();

                // Reset current session
                currentSession = null;
            }

            function updateSessionTimer() {
                if (!sessionStartTime) return;
                
                const elapsed = Math.floor((Date.now() - sessionStartTime) / 1000);
                const hours = Math.floor(elapsed / 3600);
                const minutes = Math.floor((elapsed % 3600) / 60);
                const seconds = elapsed % 60;
                
                document.getElementById('sessionTimer').textContent = 
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }

            function addSessionToList(session) {
                const sessionsList = document.getElementById('sessionsList');
                const sessionElement = document.createElement('div');
                sessionElement.className = 'card mb-3 session-card';
                sessionElement.id = `session-${session.id}`;
                
                sessionElement.innerHTML = `
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title">Session ${new Date(session.startTime).toLocaleString()}</h5>
                            <span class="badge ${session.status === 'active' ? 'bg-success' : 'bg-secondary'}">${session.status}</span>
                        </div>
                        <p class="card-text">
                            Duration: <span class="session-duration">00:00:00</span><br>
                            Fatigue Score: <span class="fatigue-score">--</span>
                        </p>
                    </div>
                `;
                
                sessionsList.insertBefore(sessionElement, sessionsList.firstChild);
            }

            function updateSessionInList(session) {
                const sessionElement = document.getElementById(`session-${session.id}`);
                if (!sessionElement) return;

                const durationSpan = sessionElement.querySelector('.session-duration');
                const fatigueScoreSpan = sessionElement.querySelector('.fatigue-score');
                
                // Calculate average fatigue score
                const avgFatigueScore = sessionData.fatigueScores.length > 0 
                    ? Math.round(sessionData.fatigueScores.reduce((a, b) => a + b) / sessionData.fatigueScores.length)
                    : 0;

                durationSpan.textContent = formatDuration(session.duration);
                fatigueScoreSpan.textContent = `${avgFatigueScore}/100`;
            }

            function formatDuration(seconds) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = seconds % 60;
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }

            function startDataCollection() {
                // Initialize data collection intervals
                setInterval(collectTypingData, 5000);
                setInterval(collectMouseData, 5000);
                setInterval(collectFacialData, 5000);
            }

            function stopDataCollection() {
                // Clear all intervals
                clearInterval(sessionTimer);
            }

            function collectTypingData() {
                if (!currentSession) return;
                
                // Collect typing metrics
                const typingData = {
                    timestamp: new Date(),
                    typingSpeed: Math.floor(Math.random() * 30) + 30, // Simulated data
                    errorRate: Math.floor(Math.random() * 10),
                    pauseFrequency: Math.floor(Math.random() * 5)
                };
                
                sessionData.typing.push(typingData);
                updateFatigueScore();
            }

            function collectMouseData() {
                if (!currentSession) return;
                
                // Collect mouse metrics
                const mouseData = {
                    timestamp: new Date(),
                    movementSpeed: Math.floor(Math.random() * 100) + 50, // Simulated data
                    clickFrequency: Math.floor(Math.random() * 10)
                };
                
                sessionData.mouse.push(mouseData);
                updateFatigueScore();
            }

            function collectFacialData() {
                if (!currentSession) return;
                
                // Collect facial metrics
                const facialData = {
                    timestamp: new Date(),
                    eyeBlinkRate: Math.floor(Math.random() * 20) + 10, // Simulated data
                    eyeClosureDuration: Math.floor(Math.random() * 300) + 100
                };
                
                sessionData.facial.push(facialData);
                updateFatigueScore();
            }

            function updateFatigueScore() {
                if (!currentSession) return;

                // Calculate fatigue score based on collected data
                let score = 0;
                
                // Typing fatigue (30% weight)
                if (sessionData.typing.length > 0) {
                    const latestTyping = sessionData.typing[sessionData.typing.length - 1];
                    if (latestTyping.typingSpeed < 40) score += 15;
                    if (latestTyping.errorRate > 5) score += 10;
                    if (latestTyping.pauseFrequency > 3) score += 5;
                }
                
                // Mouse fatigue (30% weight)
                if (sessionData.mouse.length > 0) {
                    const latestMouse = sessionData.mouse[sessionData.mouse.length - 1];
                    if (latestMouse.movementSpeed < 60) score += 15;
                    if (latestMouse.clickFrequency < 3) score += 15;
                }
                
                // Facial fatigue (40% weight)
                if (sessionData.facial.length > 0) {
                    const latestFacial = sessionData.facial[sessionData.facial.length - 1];
                    if (latestFacial.eyeBlinkRate < 15) score += 20;
                    if (latestFacial.eyeClosureDuration > 200) score += 20;
                }
                
                sessionData.fatigueScores.push(score);
                
                // Update current fatigue score display
                document.getElementById('currentFatigueScore').textContent = score;
                
                // Update fatigue level
                updateFatigueLevel(score);
            }

            function updateFatigueLevel(score) {
                const levelElement = document.getElementById('fatigueLevel');
                const iconElement = document.getElementById('fatigueIcon');
                
                let level = '';
                let icon = '';
                let color = '';
                
                if (score < 30) {
                    level = 'Low';
                    icon = 'bi-emoji-smile';
                    color = 'success';
                } else if (score < 60) {
                    level = 'Moderate';
                    icon = 'bi-emoji-neutral';
                    color = 'warning';
                } else if (score < 80) {
                    level = 'High';
                    icon = 'bi-emoji-frown';
                    color = 'danger';
                } else {
                    level = 'Severe';
                    icon = 'bi-emoji-dizzy';
                    color = 'danger';
                }
                
                levelElement.textContent = level;
                levelElement.className = `badge bg-${color}`;
                iconElement.className = `bi ${icon}`;
            }

            function generateRecommendations() {
                const recommendationsList = document.getElementById('recommendationsList');
                const avgFatigueScore = sessionData.fatigueScores.length > 0 
                    ? Math.round(sessionData.fatigueScores.reduce((a, b) => a + b) / sessionData.fatigueScores.length)
                    : 0;

                let recommendations = [];
                
                if (avgFatigueScore >= 80) {
                    recommendations = [
                        'Take an immediate break of at least 30 minutes',
                        'Step away from your computer and rest your eyes',
                        'Consider taking a short walk outside',
                        'Stay hydrated and drink water'
                    ];
                } else if (avgFatigueScore >= 60) {
                    recommendations = [
                        'Take a 15-minute break',
                        'Do some light stretching exercises',
                        'Practice deep breathing exercises',
                        'Consider adjusting your workspace ergonomics'
                    ];
                } else if (avgFatigueScore >= 30) {
                    recommendations = [
                        'Take a 5-minute break every hour',
                        'Maintain good posture',
                        'Keep your workspace well-lit',
                        'Stay hydrated'
                    ];
                } else {
                    recommendations = [
                        'Continue with your current work pattern',
                        'Maintain good posture and ergonomics',
                        'Take regular short breaks',
                        'Stay hydrated and maintain a healthy diet'
                    ];
                }

                // Clear existing recommendations
                recommendationsList.innerHTML = '';

                // Add new recommendations
                recommendations.forEach((rec, index) => {
                    const recElement = document.createElement('div');
                    recElement.className = 'card mb-3 recommendation-card';
                    recElement.innerHTML = `
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-lightbulb text-warning" style="font-size: 1.5rem;"></i>
                                </div>
                                <div>
                                    <h5 class="card-title mb-1">Recommendation ${index + 1}</h5>
                                    <p class="card-text mb-0">${rec}</p>
                                </div>
                            </div>
                        </div>
                    `;
                    recommendationsList.appendChild(recElement);
                });
            }

            function saveSessionData(session) {
                // Prepare session data for saving
                const dataToSave = {
                    session: session,
                    data: sessionData
                };
                
                // Save to localStorage for now (in a real app, this would be sent to a server)
                const savedSessions = JSON.parse(localStorage.getItem('fatigueSessions') || '[]');
                savedSessions.push(dataToSave);
                localStorage.setItem('fatigueSessions', JSON.stringify(savedSessions));
                
                // Reset session data
                sessionData = {
                    typing: [],
                    mouse: [],
                    facial: [],
                    fatigueScores: []
                };
            }

            // Load saved sessions on page load
            function loadSavedSessions() {
                const savedSessions = JSON.parse(localStorage.getItem('fatigueSessions') || '[]');
                savedSessions.forEach(session => {
                    addSessionToList(session.session);
                    updateSessionInList(session.session);
                });
            }

            // Load saved sessions when page loads
            loadSavedSessions();
        });
    </script>
</body>
</html> 