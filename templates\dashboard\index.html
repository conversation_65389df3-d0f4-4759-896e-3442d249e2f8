<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard | Mental Fatigue Detector</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 40px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .fatigue-icon {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #6a11cb;
        }
        .recommendation-card {
            border-left: 4px solid #28a745;
        }
        .insight-card {
            border-left: 4px solid #6a11cb;
        }
        .chart-container {
            position: relative;
            height: 200px;
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
            <h1 class="display-5 fw-bold">Mental Fatigue Dashboard</h1>
            <p class="lead">Monitor your mental fatigue levels and get personalized recommendations</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <div class="row mb-4">
            <!-- Fatigue Status (Simplified Indicator) -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <h2 class="card-title">FATIGUE STATUS</h2>
                        <!-- Simplified Fatigue Indicator Area -->
                        <div id="fatigueStatusIndicator" style="height: 150px; display: flex; align-items: center; justify-content: center; font-size: 2em; font-weight: bold;">
                            <!-- Content will be added by JavaScript -->
                        </div>
                        <p id="currentFatigueLevelText" class="metric-value mt-2">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Fatigue Level Over Time Chart -->
            <div class="col-md-6">
                <div class="card">
                        <div class="card-body">
                        <h3 class="card-title">FATIGUE LEVEL OVER TIME</h3>
                            <div class="chart-container">
                            <canvas id="fatigueTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <!-- Task Performance Chart -->
            <div class="col-md-6">
                <div class="card">
                        <div class="card-body">
                        <h3 class="card-title">TASK PERFORMANCE</h3>
                        <div class="chart-container">
                            <canvas id="taskPerformanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
                <div class="col-md-6">
                <div class="card recommendation-card">
                        <div class="card-body">
                        <h3 class="card-title">RECOMMENDATIONS</h3>
                        <div id="recommendationsList">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
                                                </div>
                                                </div>

        <!-- Attention Section -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card insight-card">
                    <div class="card-body">
                        <h3 class="card-title text-center">ATTENTION</h3>
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="progress" style="width: 60%; height: 25px;">
                                <div id="attentionProgressBar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <span id="attentionLevelText" class="metric-value ms-3">0%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Fetch dashboard data from API
            fetch('/api/dashboard/')
                .then(response => response.json())
                .then(data => {
                    updateFatigueDisplay(data.current_fatigue);
                    updateRecommendations(data.recent_recommendations);
                    createFatigueTrendChart(data.fatigue_history);
                    createTaskPerformanceChart(data.task_performance_data);
                    updateAttentionDisplay(data.attention_level);
                })
                .catch(error => {
                    console.error('Error fetching dashboard data:', error);
                    document.getElementById('fatigueStatusIndicator').textContent = 'Error';
                    document.getElementById('fatigueStatusIndicator').style.color = '#dc3545';
                    document.getElementById('currentFatigueLevelText').textContent = 'Error';
                    document.getElementById('currentFatigueLevelText').className = 'metric-value mt-2 text-danger';
                    document.getElementById('recommendationsList').innerHTML = '<div class="alert alert-danger">Failed to load recommendations</div>';
                    document.getElementById('attentionProgressBar').style.width = '0%';
                    document.getElementById('attentionLevelText').textContent = 'Error';
                    document.getElementById('attentionLevelText').className = 'metric-value ms-3 text-danger';
                });

            // Refresh data every 5 minutes
            setInterval(() => {
                fetch('/api/dashboard/')
                    .then(response => response.json())
                    .then(data => {
                        updateFatigueDisplay(data.current_fatigue);
                        updateRecommendations(data.recent_recommendations);
                        updateFatigueTrendChart(data.fatigue_history);
                        createTaskPerformanceChart(data.task_performance_data);
                        updateAttentionDisplay(data.attention_level);
                    })
                    .catch(error => console.error('Error refreshing dashboard data:', error));
            }, 300000); // 5 minutes
        });

        function updateFatigueDisplay(fatigueData) {
            if (!fatigueData || !fatigueData.score) {
                document.getElementById('fatigueStatusIndicator').textContent = 'No Data';
                document.getElementById('fatigueStatusIndicator').style.color = '#6c757d'; // Gray color for no data
                document.getElementById('currentFatigueLevelText').textContent = 'No Data';
                document.getElementById('currentFatigueLevelText').className = 'metric-value mt-2 text-secondary';
                return;
            }

            const score = fatigueData.score;
            const level = fatigueData.level || getFatigueLevel(score);

            const indicatorElement = document.getElementById('fatigueStatusIndicator');
            const levelTextElement = document.getElementById('currentFatigueLevelText');

            levelTextElement.textContent = level.toUpperCase();

            // Update indicator color and text based on fatigue level
            if (score < 30) {
                indicatorElement.textContent = 'LOW';
                indicatorElement.style.color = '#28a745'; // Green
                levelTextElement.className = 'metric-value mt-2 text-success';
            } else if (score < 60) {
                indicatorElement.textContent = 'MEDIUM';
                indicatorElement.style.color = '#ffc107'; // Yellow
                levelTextElement.className = 'metric-value mt-2 text-warning';
            } else if (score < 80) {
                indicatorElement.textContent = 'HIGH';
                indicatorElement.style.color = '#fd7e14'; // Orange
                levelTextElement.className = 'metric-value mt-2 text-danger';
            } else {
                indicatorElement.textContent = 'VERY HIGH';
                indicatorElement.style.color = '#dc3545'; // Red
                levelTextElement.className = 'metric-value mt-2 text-danger';
            }
        }

        function getFatigueLevel(score) {
            if (score < 30) return 'Low';
            if (score < 60) return 'Medium';
            if (score < 80) return 'High';
            return 'Very High';
        }

        function updateRecommendations(recommendations) {
            const recommendationsList = document.getElementById('recommendationsList');
            recommendationsList.innerHTML = ''; // Clear previous recommendations

            if (!recommendations || recommendations.length === 0) {
                recommendationsList.innerHTML = '<div class="alert alert-info">No recommendations available</div>';
                return;
            }

            const ul = document.createElement('ul');
            ul.className = 'list-unstyled';
            recommendations.forEach(rec => {
                const li = document.createElement('li');
                li.textContent = `• ${rec}`; // Assuming recommendations are strings
                ul.appendChild(li);
            });
            recommendationsList.appendChild(ul);
        }

        function updateContributingFactors(factors) {
            // This section was removed based on the new image layout.
            // If needed, reintegrate this logic into recommendations or insights.
        }

        let fatigueTrendChartInstance = null;

        function createFatigueTrendChart(fatigueHistory) {
            const ctx = document.getElementById('fatigueTrendChart').getContext('2d');

            if (fatigueTrendChartInstance) {
                fatigueTrendChartInstance.destroy();
            }

            const labels = fatigueHistory.map(item => new Date(item.timestamp).toLocaleTimeString()); // Format time
            const scores = fatigueHistory.map(item => item.score);

            fatigueTrendChartInstance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
            datasets: [{
                label: 'Fatigue Score',
                        data: scores,
                        borderColor: '#2575fc',
                        backgroundColor: 'rgba(37, 117, 252, 0.2)',
                        tension: 0.3,
                        fill: true
                    }]
                },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                            max: 100
                        }
                    },
                    plugins: {
                        legend: {
                            display: true
                        }
                    }
                }
            });
        }

        function updateFatigueTrendChart(fatigueHistory) {
             if (fatigueTrendChartInstance) {
                const labels = fatigueHistory.map(item => new Date(item.timestamp).toLocaleTimeString());
                const scores = fatigueHistory.map(item => item.score);

                fatigueTrendChartInstance.data.labels = labels;
                fatigueTrendChartInstance.data.datasets[0].data = scores;
                fatigueTrendChartInstance.update();
            } else {
                createFatigueTrendChart(fatigueHistory);
            }
        }

        let taskPerformanceChartInstance = null;

        function createTaskPerformanceChart(taskPerformanceData) {
             const ctx = document.getElementById('taskPerformanceChart').getContext('2d');

            if (taskPerformanceChartInstance) {
                taskPerformanceChartInstance.destroy();
            }
            
            // Assuming taskPerformanceData is an array of { label: 'Task X', performance: Y }
            const labels = taskPerformanceData.map(item => item.label);
            const performanceScores = taskPerformanceData.map(item => item.performance);

            taskPerformanceChartInstance = new Chart(ctx, {
                type: 'bar',
                    data: {
                    labels: labels,
                        datasets: [{
                        label: 'Task Performance',
                        data: performanceScores,
                        backgroundColor: '#6a11cb',
                            borderColor: '#6a11cb',
                        borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                            max: 100
                        }
                    },
                     plugins: {
                        legend: {
                            display: true
                        }
                    }
                }
            });
        }

        function updateDetailedInsights(data) {
            // These sections (Productivity, Focus, Stress) were removed based on the new image layout.
            // If needed, reintegrate this logic elsewhere.
        }

        function updateAttentionDisplay(attentionData) {
            if (!attentionData || attentionData.level === undefined) {
                document.getElementById('attentionProgressBar').style.width = '0%';
                document.getElementById('attentionProgressBar').setAttribute('aria-valuenow', 0);
                document.getElementById('attentionLevelText').textContent = 'No Data';
                 document.getElementById('attentionLevelText').className = 'metric-value ms-3 text-secondary';
                return;
            }

            const attentionLevel = attentionData.level;
            const progressBar = document.getElementById('attentionProgressBar');
            const levelText = document.getElementById('attentionLevelText');

            progressBar.style.width = `${attentionLevel}%`;
            progressBar.setAttribute('aria-valuenow', attentionLevel);
            levelText.textContent = `${attentionLevel}%`;

            // Optionally, change progress bar color based on attention level
            if (attentionLevel < 40) {
                progressBar.className = 'progress-bar bg-danger';
            } else if (attentionLevel < 70) {
                progressBar.className = 'progress-bar bg-warning';
            } else {
                progressBar.className = 'progress-bar bg-success';
            }
        }
    </script>
</body>
</html>
