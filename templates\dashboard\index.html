<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard | Mental Fatigue Detector</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <meta name="csrf-token" content="{{ csrf_token }}">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 25px;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 20px;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .fatigue-status-card {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            text-align: center;
            padding: 40px 20px;
        }

        .fatigue-score {
            font-size: 4rem;
            font-weight: 800;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .fatigue-level {
            font-size: 1.5rem;
            font-weight: 600;
            margin-top: 10px;
        }

        .metric-card {
            text-align: center;
            padding: 25px;
        }

        .metric-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #6a11cb;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #7f8c8d;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.9rem;
        }

        .recommendation-item {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 5px solid #28a745;
        }

        .insight-item {
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 5px solid #6a11cb;
        }

        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
            padding: 20px;
        }

        .progress-custom {
            height: 25px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.3);
            overflow: hidden;
        }

        .progress-bar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            transition: width 0.6s ease;
        }

        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
        }

        .status-low { background: #28a745; }
        .status-moderate { background: #ffc107; }
        .status-high { background: #fd7e14; }
        .status-severe { background: #dc3545; }

        .data-quality-indicator {
            display: inline-flex;
            align-items: center;
            margin: 5px 10px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .quality-good {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .quality-poor {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .ml-confidence {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            font-weight: 600;
        }

        .section-title {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #6a11cb;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="header">
            <h1 class="display-4 fw-bold mb-3">
                <i class="bi bi-brain me-3"></i>Mental Fatigue Dashboard
            </h1>
            <p class="lead mb-0">AI-Powered Analysis & Personalized Insights</p>
            <div class="mt-3">
                <span class="ml-confidence" id="mlConfidenceDisplay">
                    <i class="bi bi-cpu me-2"></i>ML Confidence: <span id="mlConfidence">Loading...</span>
                </span>
            </div>
        </div>

        <!-- Main Fatigue Status -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card fatigue-status-card">
                    <div class="card-body">
                        <h2 class="mb-4">
                            <i class="bi bi-activity me-2"></i>Current Fatigue Level
                        </h2>
                        <div class="fatigue-score" id="fatigueScoreDisplay">--</div>
                        <div class="fatigue-level" id="fatigueLevelDisplay">Analyzing...</div>
                        <div class="mt-4">
                            <span class="status-indicator" id="statusIndicator"></span>
                            <span id="statusText">Collecting data...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-clipboard-data me-2"></i>Data Quality
                    </div>
                    <div class="card-body">
                        <div id="dataQualityIndicators">
                            <div class="data-quality-indicator quality-poor" id="typingDataQuality">
                                <i class="bi bi-keyboard me-2"></i>Typing: Not Available
                            </div>
                            <div class="data-quality-indicator quality-poor" id="mouseDataQuality">
                                <i class="bi bi-mouse me-2"></i>Mouse: Not Available
                            </div>
                            <div class="data-quality-indicator quality-poor" id="facialDataQuality">
                                <i class="bi bi-camera me-2"></i>Facial: Not Available
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Individual Component Scores -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body metric-card">
                        <div class="metric-icon">
                            <i class="bi bi-keyboard"></i>
                        </div>
                        <div class="metric-value" id="typingFatigueScore">--</div>
                        <div class="metric-label">Typing Fatigue</div>
                        <div class="mt-3">
                            <small class="text-muted">
                                Speed: <span id="typingSpeedDisplay">--</span> WPM<br>
                                Errors: <span id="errorRateDisplay">--</span>%<br>
                                Pauses: <span id="pauseFrequencyDisplay">--</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body metric-card">
                        <div class="metric-icon">
                            <i class="bi bi-mouse"></i>
                        </div>
                        <div class="metric-value" id="mouseFatigueScore">--</div>
                        <div class="metric-label">Mouse Fatigue</div>
                        <div class="mt-3">
                            <small class="text-muted">
                                Score: <span id="mouseScoreDisplay">--</span><br>
                                Reaction: <span id="reactionTimeDisplay">--</span>ms<br>
                                Accuracy: <span id="accuracyDisplay">--</span>%
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body metric-card">
                        <div class="metric-icon">
                            <i class="bi bi-camera"></i>
                        </div>
                        <div class="metric-value" id="facialFatigueScore">--</div>
                        <div class="metric-label">Facial Fatigue</div>
                        <div class="mt-3">
                            <small class="text-muted">
                                Blinks: <span id="blinkRateDisplay">--</span>/min<br>
                                Closure: <span id="eyeClosureDisplay">--</span>ms<br>
                                Expression: <span id="facialExpressionDisplay">--</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-graph-up me-2"></i>Fatigue Trend Analysis
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="fatigueTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-bar-chart me-2"></i>Component Breakdown
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="componentChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommendations and Insights -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-lightbulb me-2"></i>AI Recommendations
                    </div>
                    <div class="card-body">
                        <div id="mlRecommendations">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading recommendations...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-eye me-2"></i>ML Insights
                    </div>
                    <div class="card-body">
                        <div id="mlInsights">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading insights...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row">
            <div class="col-12 text-center">
                <button class="btn btn-primary btn-lg me-3" onclick="window.location.href='/data_collection/'">
                    <i class="bi bi-arrow-clockwise me-2"></i>Retake Assessment
                </button>
                <button class="btn btn-outline-primary btn-lg" onclick="exportData()">
                    <i class="bi bi-download me-2"></i>Export Data
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables for charts
        let fatigueTrendChart = null;
        let componentChart = null;

        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dashboard loading...');

            // Load data from session storage first
            loadFatigueDataFromSession();

            // Load ML analysis results
            loadMLAnalysisFromSession();

            // Initialize charts with default data
            initializeCharts();

            console.log('Dashboard initialized');
        });

        // Load fatigue data from session storage
        function loadFatigueDataFromSession() {
            console.log('Loading fatigue data from session storage...');

            const fatigueScore = sessionStorage.getItem('fatigueScore');
            const fatigueLevel = sessionStorage.getItem('fatigueLevel');
            const typingData = JSON.parse(sessionStorage.getItem('typingData') || 'null');
            const mouseData = JSON.parse(sessionStorage.getItem('mouseData') || 'null');
            const facialData = JSON.parse(sessionStorage.getItem('facialData') || 'null');
            const typingFatigueScore = parseFloat(sessionStorage.getItem('typingFatigueScore')) || 0;
            const mouseFatigueScore = parseFloat(sessionStorage.getItem('mouseFatigueScore')) || 0;
            const facialFatigueScore = parseFloat(sessionStorage.getItem('facialFatigueScore')) || 0;

            console.log('Session data:', {
                fatigueScore,
                fatigueLevel,
                typingData,
                mouseData,
                facialData,
                typingFatigueScore,
                mouseFatigueScore,
                facialFatigueScore
            });

            if (fatigueScore) {
                const combinedScore = parseFloat(fatigueScore);

                // Update main fatigue display
                updateMainFatigueDisplay(combinedScore, fatigueLevel);

                // Update individual component scores
                updateComponentScores(typingFatigueScore, mouseFatigueScore, facialFatigueScore);

                // Update detailed metrics
                updateDetailedMetrics(typingData, mouseData, facialData);

                // Update data quality indicators
                updateDataQualityIndicators(typingData, mouseData, facialData);

                // Update charts with real data
                updateChartsWithData(typingFatigueScore, mouseFatigueScore, facialFatigueScore);

                console.log('Dashboard updated with session data');
            } else {
                console.log('No fatigue data found in session storage');
                showNoDataState();
            }
        }

        // Update main fatigue display
        function updateMainFatigueDisplay(score, level) {
            const scoreDisplay = document.getElementById('fatigueScoreDisplay');
            const levelDisplay = document.getElementById('fatigueLevelDisplay');
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');

            if (scoreDisplay) scoreDisplay.textContent = Math.round(score);
            if (levelDisplay) levelDisplay.textContent = level || getFatigueLevel(score);

            // Update status indicator
            if (statusIndicator && statusText) {
                let statusClass = '';
                let statusMessage = '';

                if (score < 30) {
                    statusClass = 'status-low';
                    statusMessage = 'Low fatigue - You\'re doing great!';
                } else if (score < 60) {
                    statusClass = 'status-moderate';
                    statusMessage = 'Moderate fatigue - Consider a short break';
                } else if (score < 80) {
                    statusClass = 'status-high';
                    statusMessage = 'High fatigue - Take a break soon';
                } else {
                    statusClass = 'status-severe';
                    statusMessage = 'Severe fatigue - Take a break now';
                }

                statusIndicator.className = 'status-indicator ' + statusClass;
                statusText.textContent = statusMessage;
            }
        }

        // Update individual component scores
        function updateComponentScores(typingScore, mouseScore, facialScore) {
            const typingElement = document.getElementById('typingFatigueScore');
            const mouseElement = document.getElementById('mouseFatigueScore');
            const facialElement = document.getElementById('facialFatigueScore');

            if (typingElement) {
                typingElement.textContent = Math.round(typingScore);
                typingElement.className = 'metric-value ' + getScoreColorClass(typingScore);
            }

            if (mouseElement) {
                mouseElement.textContent = Math.round(mouseScore);
                mouseElement.className = 'metric-value ' + getScoreColorClass(mouseScore);
            }

            if (facialElement) {
                facialElement.textContent = Math.round(facialScore);
                facialElement.className = 'metric-value ' + getScoreColorClass(facialScore);
            }
        }

        // Update detailed metrics
        function updateDetailedMetrics(typingData, mouseData, facialData) {
            // Update typing metrics
            if (typingData) {
                const speedElement = document.getElementById('typingSpeedDisplay');
                const errorElement = document.getElementById('errorRateDisplay');
                const pauseElement = document.getElementById('pauseFrequencyDisplay');

                if (speedElement) speedElement.textContent = typingData.typingSpeed || '--';
                if (errorElement) errorElement.textContent = typingData.errorRate || '--';
                if (pauseElement) pauseElement.textContent = typingData.pauseFrequency || '--';
            }

            // Update mouse metrics
            if (mouseData) {
                const scoreElement = document.getElementById('mouseScoreDisplay');
                const reactionElement = document.getElementById('reactionTimeDisplay');
                const accuracyElement = document.getElementById('accuracyDisplay');

                if (scoreElement) scoreElement.textContent = mouseData.score || '--';
                if (reactionElement) reactionElement.textContent = Math.round(mouseData.reactionTime) || '--';
                if (accuracyElement) accuracyElement.textContent = Math.round(mouseData.accuracy) || '--';
            }

            // Update facial metrics
            if (facialData) {
                const blinkElement = document.getElementById('blinkRateDisplay');
                const closureElement = document.getElementById('eyeClosureDisplay');
                const expressionElement = document.getElementById('facialExpressionDisplay');

                if (blinkElement) blinkElement.textContent = facialData.blinkRate || '--';
                if (closureElement) closureElement.textContent = facialData.eyeClosure || '--';
                if (expressionElement) expressionElement.textContent = facialData.expression || '--';
            }
        }

        // Update data quality indicators
        function updateDataQualityIndicators(typingData, mouseData, facialData) {
            const typingQuality = document.getElementById('typingDataQuality');
            const mouseQuality = document.getElementById('mouseDataQuality');
            const facialQuality = document.getElementById('facialDataQuality');

            if (typingQuality) {
                if (typingData && typingData.typingSpeed > 0) {
                    typingQuality.className = 'data-quality-indicator quality-good';
                    typingQuality.innerHTML = '<i class="bi bi-keyboard me-2"></i>Typing: Available';
                } else {
                    typingQuality.className = 'data-quality-indicator quality-poor';
                    typingQuality.innerHTML = '<i class="bi bi-keyboard me-2"></i>Typing: Not Available';
                }
            }

            if (mouseQuality) {
                if (mouseData && mouseData.score > 0) {
                    mouseQuality.className = 'data-quality-indicator quality-good';
                    mouseQuality.innerHTML = '<i class="bi bi-mouse me-2"></i>Mouse: Available';
                } else {
                    mouseQuality.className = 'data-quality-indicator quality-poor';
                    mouseQuality.innerHTML = '<i class="bi bi-mouse me-2"></i>Mouse: Not Available';
                }
            }

            if (facialQuality) {
                if (facialData && facialData.analyzed) {
                    facialQuality.className = 'data-quality-indicator quality-good';
                    facialQuality.innerHTML = '<i class="bi bi-camera me-2"></i>Facial: Available';
                } else {
                    facialQuality.className = 'data-quality-indicator quality-poor';
                    facialQuality.innerHTML = '<i class="bi bi-camera me-2"></i>Facial: Not Available';
                }
            }
        }

        // Helper functions
        function getFatigueLevel(score) {
            if (score < 30) return 'Low';
            if (score < 60) return 'Moderate';
            if (score < 80) return 'High';
            return 'Severe';
        }

        function getScoreColorClass(score) {
            if (score < 30) return 'text-success';
            if (score < 60) return 'text-warning';
            if (score < 80) return 'text-danger';
            return 'text-dark';
        }

        // Load ML analysis results
        function loadMLAnalysisFromSession() {
            const mlAnalysis = JSON.parse(sessionStorage.getItem('mlAnalysis') || 'null');
            const mlRecommendations = JSON.parse(sessionStorage.getItem('mlRecommendations') || 'null');

            console.log('Loading ML analysis from session:', {
                mlAnalysis: mlAnalysis,
                mlRecommendations: mlRecommendations
            });

            if (mlAnalysis) {
                console.log('ML Analysis found:', mlAnalysis);

                // Update ML confidence
                displayMLConfidence(mlAnalysis.confidence);

                // Update recommendations
                if (mlRecommendations) {
                    updateRecommendationsDisplay(mlRecommendations);
                    updateInsightsDisplay(mlRecommendations);
                }
            } else {
                console.log('No ML analysis found, checking for confidence in other session data');

                // Try to get confidence from other session storage keys
                const confidence = sessionStorage.getItem('mlConfidence');
                if (confidence) {
                    displayMLConfidence(parseFloat(confidence));
                } else {
                    // Set default confidence if no ML data available
                    displayMLConfidence(0.85); // Default 85% confidence
                }

                showDefaultRecommendations();
            }
        }

        // Display ML confidence
        function displayMLConfidence(confidence) {
            const confidenceElement = document.getElementById('mlConfidence');
            const confidenceDisplay = document.getElementById('mlConfidenceDisplay');

            console.log('Displaying ML confidence:', confidence);

            if (confidenceElement) {
                let confidencePercent;

                // Handle different confidence formats
                if (confidence > 1) {
                    // Already in percentage format
                    confidencePercent = Math.round(confidence);
                } else {
                    // Convert from decimal to percentage
                    confidencePercent = Math.round(confidence * 100);
                }

                confidenceElement.textContent = confidencePercent + '%';

                // Show the confidence display
                if (confidenceDisplay) {
                    confidenceDisplay.style.display = 'inline-block';
                }

                console.log('ML confidence updated to:', confidencePercent + '%');
            } else {
                console.error('ML confidence element not found');
            }
        }

        // Update recommendations display
        function updateRecommendationsDisplay(mlRecommendations) {
            const container = document.getElementById('mlRecommendations');
            if (!container || !mlRecommendations || !mlRecommendations.recommendations) {
                return;
            }

            container.innerHTML = '';

            mlRecommendations.recommendations.forEach(rec => {
                const recElement = document.createElement('div');
                recElement.className = 'recommendation-item';

                const priorityClass = {
                    'low': 'text-success',
                    'medium': 'text-warning',
                    'high': 'text-danger',
                    'urgent': 'text-danger fw-bold'
                }[rec.priority] || 'text-info';

                recElement.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="fw-bold mb-2">${rec.type}</h6>
                            <p class="mb-2">${rec.description}</p>
                            <small class="text-muted">Duration: ${rec.duration}</small>
                        </div>
                        <span class="badge ${priorityClass}">${rec.priority.toUpperCase()}</span>
                    </div>
                `;

                container.appendChild(recElement);
            });
        }

        // Update insights display
        function updateInsightsDisplay(mlRecommendations) {
            const container = document.getElementById('mlInsights');
            if (!container || !mlRecommendations || !mlRecommendations.insights) {
                return;
            }

            container.innerHTML = '';

            if (mlRecommendations.insights.length > 0) {
                mlRecommendations.insights.forEach(insight => {
                    const insightElement = document.createElement('div');
                    insightElement.className = 'insight-item';
                    insightElement.innerHTML = `
                        <i class="bi bi-lightbulb text-warning me-2"></i>
                        ${insight}
                    `;
                    container.appendChild(insightElement);
                });
            } else {
                container.innerHTML = '<div class="text-muted text-center">No specific insights available</div>';
            }
        }

        // Show default recommendations when no ML data is available
        function showDefaultRecommendations() {
            const recommendationsContainer = document.getElementById('mlRecommendations');
            const insightsContainer = document.getElementById('mlInsights');

            if (recommendationsContainer) {
                recommendationsContainer.innerHTML = `
                    <div class="recommendation-item">
                        <h6 class="fw-bold mb-2">Complete Assessment</h6>
                        <p class="mb-2">Take the full assessment to get personalized recommendations.</p>
                        <small class="text-muted">Duration: 5-10 minutes</small>
                    </div>
                `;
            }

            if (insightsContainer) {
                insightsContainer.innerHTML = `
                    <div class="insight-item">
                        <i class="bi bi-info-circle text-info me-2"></i>
                        Complete the data collection process to receive AI-powered insights
                    </div>
                `;
            }
        }

        // Show no data state
        function showNoDataState() {
            const scoreDisplay = document.getElementById('fatigueScoreDisplay');
            const levelDisplay = document.getElementById('fatigueLevelDisplay');
            const statusText = document.getElementById('statusText');

            if (scoreDisplay) scoreDisplay.textContent = '--';
            if (levelDisplay) levelDisplay.textContent = 'No Data';
            if (statusText) statusText.textContent = 'Complete the assessment to see your fatigue level';

            showDefaultRecommendations();
        }

        // Initialize charts
        function initializeCharts() {
            initializeFatigueTrendChart();
            initializeComponentChart();
        }

        // Initialize fatigue trend chart
        function initializeFatigueTrendChart() {
            const ctx = document.getElementById('fatigueTrendChart');
            if (!ctx) return;

            fatigueTrendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Start', '25%', '50%', '75%', 'End'],
                    datasets: [{
                        label: 'Fatigue Level',
                        data: [30, 35, 45, 50, 55],
                        borderColor: '#6a11cb',
                        backgroundColor: 'rgba(106, 17, 203, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Fatigue Score'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        // Initialize component chart
        function initializeComponentChart() {
            const ctx = document.getElementById('componentChart');
            if (!ctx) return;

            componentChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Typing', 'Mouse', 'Facial'],
                    datasets: [{
                        data: [30, 30, 30],
                        backgroundColor: [
                            '#6a11cb',
                            '#2575fc',
                            '#667eea'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Update charts with real data
        function updateChartsWithData(typingScore, mouseScore, facialScore) {
            // Update component chart
            if (componentChart) {
                componentChart.data.datasets[0].data = [typingScore, mouseScore, facialScore];
                componentChart.update();
            }

            // Update trend chart with simulated progression
            if (fatigueTrendChart) {
                const avgScore = (typingScore + mouseScore + facialScore) / 3;
                const progression = generateFatigueProgression(avgScore);
                fatigueTrendChart.data.datasets[0].data = progression;
                fatigueTrendChart.update();
            }
        }

        // Generate fatigue progression
        function generateFatigueProgression(finalScore) {
            const variation = 10;
            return [
                Math.max(0, finalScore - 20 + Math.random() * variation),
                Math.max(0, finalScore - 15 + Math.random() * variation),
                Math.max(0, finalScore - 10 + Math.random() * variation),
                Math.max(0, finalScore - 5 + Math.random() * variation),
                finalScore
            ];
        }

        // Export data function
        function exportData() {
            const data = {
                fatigueScore: sessionStorage.getItem('fatigueScore'),
                fatigueLevel: sessionStorage.getItem('fatigueLevel'),
                typingData: JSON.parse(sessionStorage.getItem('typingData') || 'null'),
                mouseData: JSON.parse(sessionStorage.getItem('mouseData') || 'null'),
                facialData: JSON.parse(sessionStorage.getItem('facialData') || 'null'),
                mlAnalysis: JSON.parse(sessionStorage.getItem('mlAnalysis') || 'null'),
                timestamp: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `fatigue-analysis-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
